package com.ruoyi.fat.controller;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import jakarta.servlet.http.HttpServletResponse;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.fat.domain.*;
import com.ruoyi.fat.service.*;
import com.ruoyi.web.domain.CaszPatient;
import com.ruoyi.web.service.ICaszPatientService;
import com.ruoyi.web.utils.DoubleListUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.aspectj.weaver.loadtime.Aj;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.im.domain.AIMessage;
import com.ruoyi.web.service.IAssistantRecordService;
import com.ruoyi.web.service.IImProhibitService;
import com.ruoyi.web.utils.AIUtils;
import com.ruoyi.web.domain.AssistantRecord;
import com.ruoyi.web.domain.ImProhibit;

/**
 * 体重指标数据Controller
 *
 * <AUTHOR>
 * @date 2024-08-09
 */
@RestController
@RequestMapping("/sign/signRecord")
public class FatSignRecordController extends BaseController {

    @Autowired
    private IFatSignRecordService fatSignRecordService;
    @Autowired
    private ICaszPatientService caszPatientService;
    @Autowired
    private IFatPatientSportService patientSportService;
    @Autowired
    private IFatScoreService scoreService;
    @Autowired
    private IFatEatService eatService;
    @Autowired
    private IFatEatItemService eatItemService;
    @Autowired
    private IAssistantRecordService assistantRecordService;
    @Autowired
    private IImProhibitService imProhibitService;
    @Autowired
    private AIUtils aiUtils;

    /**
     * 查看所有体征数据 - 支持分页
     *
     * @param patientId 患者ID
     * @param signType 体征类型：体重、腰围、运动、饮食、血压、血糖
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 分页数据
     */
    @GetMapping("/getAllVitalData")
    public TableDataInfo getAllVitalData(
            @RequestParam String patientId,
            @RequestParam String signType,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {

        startPage();
        List<Map<String, Object>> resultList = new ArrayList<>();

        try {
            switch (signType) {
                case "体重":
                case "腰围":
                    resultList = getSignRecordData(patientId, signType);
                    break;
                case "运动":
                    resultList = getSportData(patientId);
                    break;
                case "饮食":
                    resultList = getDietData(patientId);
                    break;
                case "血压":
                    resultList = getBloodPressureData(patientId);
                    break;
                case "血糖":
                    resultList = getBloodSugarData(patientId);
                    break;
                default:
                    return getDataTable(new ArrayList<>());
            }
        } catch (Exception e) {
            return getDataTable(new ArrayList<>());
        }

        return getDataTable(resultList);
    }

    /**
     * 获取体重/腰围数据 - 按日期分组，保留每天最后一条记录
     */
    private List<Map<String, Object>> getSignRecordData(String patientId, String signType) {
        LambdaQueryWrapper<FatSignRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FatSignRecord::getSignName, signType);
        wrapper.eq(FatSignRecord::getPatientId, patientId);
        wrapper.orderByDesc(FatSignRecord::getSignTime);

        List<FatSignRecord> list = fatSignRecordService.list(wrapper);
        List<Map<String, Object>> resultList = new ArrayList<>();

        // 按日期分组，保留每天最后一条记录
        Map<String, FatSignRecord> groupedData = new LinkedHashMap<>();
        for (FatSignRecord record : list) {
            String dateKey = DateUtils.parseDateToStr("yyyy-MM-dd", record.getSignTime());
            // 如果该日期还没有记录，或者当前记录的时间更晚，则保留当前记录
            if (!groupedData.containsKey(dateKey) ||
                record.getSignTime().after(groupedData.get(dateKey).getSignTime())) {
                groupedData.put(dateKey, record);
            }
        }

        // 按日期倒序排列
        List<String> sortedDates = new ArrayList<>(groupedData.keySet());
        sortedDates.sort(Collections.reverseOrder());

        for (String dateKey : sortedDates) {
            FatSignRecord record = groupedData.get(dateKey);
            try {
                JSONObject formValue = JSONObject.parseObject(record.getFormValue());
                String vitalTime = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm", record.getSignTime());
                String registerTime = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm", record.getCreateTime() != null ? record.getCreateTime() : record.getSignTime());

                Map<String, Object> item = new HashMap<>();
                item.put("date", vitalTime);

                if ("体重".equals(signType)) {
                    String weight = formValue.getString("weight");
                    if (weight != null && !weight.isEmpty()) {
                        item.put("value", weight + " kg");
                        item.put("displayText", "体征时间：" + vitalTime + " | 登记时间：" + registerTime + " | 数值：" + weight + " kg");
                        resultList.add(item);
                    }
                } else if ("腰围".equals(signType)) {
                    String waist = formValue.getString("waist");
                    if (waist != null && !waist.isEmpty()) {
                        item.put("value", waist + " cm");
                        item.put("displayText", "体征时间：" + vitalTime + " | 登记时间：" + registerTime + " | 数值：" + waist + " cm");
                        resultList.add(item);
                    }
                }
            } catch (Exception e) {
                // 数据解析失败时跳过该记录
                continue;
            }
        }

        return resultList;
    }

    /**
     * 获取运动数据
     */
    private List<Map<String, Object>> getSportData(String patientId) {
        LambdaQueryWrapper<FatPatientSport> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FatPatientSport::getPatientId, patientId);
        wrapper.orderByDesc(FatPatientSport::getSportDate);

        List<FatPatientSport> list = patientSportService.list(wrapper);
        List<Map<String, Object>> resultList = new ArrayList<>();

        // 按日期分组统计
        TreeMap<Date, List<FatPatientSport>> groupedData = new TreeMap<>(Collections.reverseOrder());
        for (FatPatientSport sport : list) {
            groupedData.computeIfAbsent(sport.getSportDate(), k -> new ArrayList<>()).add(sport);
        }

        for (Map.Entry<Date, List<FatPatientSport>> entry : groupedData.entrySet()) {
            String vitalTime = DateUtils.parseDateToStr("yyyy-MM-dd", entry.getKey());

            BigDecimal totalEnergy = entry.getValue().stream()
                    .map(FatPatientSport::getEnergy)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 获取最早的创建时间作为登记时间
            Date earliestCreateTime = entry.getValue().stream()
                    .map(FatPatientSport::getCreateTime)
                    .filter(Objects::nonNull)
                    .min(Date::compareTo)
                    .orElse(entry.getKey());
            String registerTime = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm", earliestCreateTime);

            Map<String, Object> item = new HashMap<>();
            item.put("date", vitalTime);
            item.put("value", totalEnergy + " kcal");
            item.put("displayText", "体征时间：" + vitalTime + " | 登记时间：" + registerTime + " | 数值：" + totalEnergy + " kcal");

            resultList.add(item);
        }

        return resultList;
    }

    /**
     * 获取饮食数据
     */
    private List<Map<String, Object>> getDietData(String patientId) {
        LambdaQueryWrapper<FatEat> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FatEat::getPatientId, patientId);
        wrapper.orderByDesc(FatEat::getEatTime);

        List<FatEat> list = eatService.list(wrapper);
        List<Map<String, Object>> resultList = new ArrayList<>();

        // 按日期分组统计
        TreeMap<Date, List<FatEat>> groupedData = new TreeMap<>(Collections.reverseOrder());
        for (FatEat eat : list) {
            groupedData.computeIfAbsent(eat.getEatTime(), k -> new ArrayList<>()).add(eat);
        }

        for (Map.Entry<Date, List<FatEat>> entry : groupedData.entrySet()) {
            String vitalTime = DateUtils.parseDateToStr("yyyy-MM-dd", entry.getKey());

            // 获取该日期所有饮食记录的总能量
            List<String> eatIds = entry.getValue().stream()
                    .map(FatEat::getId)
                    .collect(Collectors.toList());

            BigDecimal totalEnergy = BigDecimal.ZERO;
            if (!eatIds.isEmpty()) {
                LambdaQueryWrapper<FatEatItem> itemWrapper = new LambdaQueryWrapper<>();
                itemWrapper.in(FatEatItem::getEatId, eatIds);
                List<FatEatItem> eatItems = eatItemService.list(itemWrapper);

                totalEnergy = eatItems.stream()
                        .map(FatEatItem::getEnergy)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            // 获取最早的创建时间作为登记时间
            Date earliestCreateTime = entry.getValue().stream()
                    .map(FatEat::getCreateTime)
                    .filter(Objects::nonNull)
                    .min(Date::compareTo)
                    .orElse(entry.getKey());
            String registerTime = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm", earliestCreateTime);

            Map<String, Object> item = new HashMap<>();
            item.put("date", vitalTime);
            item.put("value", totalEnergy + " kcal");
            item.put("displayText", "体征时间：" + vitalTime + " | 登记时间：" + registerTime + " | 数值：" + totalEnergy + " kcal");

            resultList.add(item);
        }

        return resultList;
    }

    /**
     * 获取血压数据 - 按日期分组，保留每天最后一条记录
     */
    private List<Map<String, Object>> getBloodPressureData(String patientId) {
        List<Map<String, Object>> resultList = new ArrayList<>();

        try {
            // 查询血压记录
            LambdaQueryWrapper<FatSignRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FatSignRecord::getSignName, "血压");
            wrapper.eq(FatSignRecord::getPatientId, patientId);
            wrapper.orderByDesc(FatSignRecord::getSignTime);

            List<FatSignRecord> bloodPressureRecords = fatSignRecordService.list(wrapper);

            // 按日期分组，保留每天最后一条记录
            Map<String, FatSignRecord> groupedData = new LinkedHashMap<>();
            for (FatSignRecord record : bloodPressureRecords) {
                String dateKey = DateUtils.parseDateToStr("yyyy-MM-dd", record.getSignTime());
                // 如果该日期还没有记录，或者当前记录的时间更晚，则保留当前记录
                if (!groupedData.containsKey(dateKey) ||
                    record.getSignTime().after(groupedData.get(dateKey).getSignTime())) {
                    groupedData.put(dateKey, record);
                }
            }

            // 按日期倒序排列
            List<String> sortedDates = new ArrayList<>(groupedData.keySet());
            sortedDates.sort(Collections.reverseOrder());

            for (String dateKey : sortedDates) {
                FatSignRecord record = groupedData.get(dateKey);
                try {
                    String systolicValue = null;
                    String diastolicValue = null;
                    String signTime = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm", record.getSignTime());

                    // 优先尝试解析form_value字段
                    if (record.getFormValue() != null && !record.getFormValue().isEmpty()) {
                        try {
                            JSONObject formValueData = JSONObject.parseObject(record.getFormValue());
                            if (formValueData.containsKey("ssy")) {
                                systolicValue = formValueData.getString("ssy");
                            }
                            if (formValueData.containsKey("szy")) {
                                diastolicValue = formValueData.getString("szy");
                            }
                            // 如果form_value中有bloodPressure字段，直接使用
                            if (formValueData.containsKey("bloodPressure")) {
                                String bloodPressure = formValueData.getString("bloodPressure");
                                if (bloodPressure != null && bloodPressure.contains("/")) {
                                    String[] parts = bloodPressure.split("/");
                                    if (parts.length == 2) {
                                        systolicValue = parts[0].trim();
                                        diastolicValue = parts[1].trim();
                                    }
                                }
                            }
                        } catch (Exception e) {
                            // form_value解析失败，继续尝试form_text
                        }
                    }

                    // 如果form_value解析失败，尝试解析form_text字段
                    if ((systolicValue == null || systolicValue.isEmpty()) &&
                        (diastolicValue == null || diastolicValue.isEmpty()) &&
                        record.getFormText() != null && !record.getFormText().isEmpty()) {
                        try {
                            JSONObject formTextData = JSONObject.parseObject(record.getFormText());
                            if (formTextData.containsKey("signTime")) {
                                signTime = formTextData.getString("signTime");
                            }
                            if (formTextData.containsKey("ssy")) {
                                systolicValue = formTextData.getString("ssy");
                            }
                            if (formTextData.containsKey("szy")) {
                                diastolicValue = formTextData.getString("szy");
                            }
                        } catch (Exception e) {
                            // form_text解析也失败，跳过该记录
                            continue;
                        }
                    }

                    // 组合血压值显示
                    if ((systolicValue != null && !systolicValue.isEmpty() && !"0".equals(systolicValue)) ||
                        (diastolicValue != null && !diastolicValue.isEmpty() && !"0".equals(diastolicValue))) {

                        String systolic = (systolicValue != null && !systolicValue.isEmpty() && !"0".equals(systolicValue)) ? systolicValue : "--";
                        String diastolic = (diastolicValue != null && !diastolicValue.isEmpty() && !"0".equals(diastolicValue)) ? diastolicValue : "--";

                        String registerTime = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm", record.getCreateTime() != null ? record.getCreateTime() : record.getSignTime());
                        String vitalTime = signTime.contains(":") ? signTime : signTime + " 00:00";

                        // 分别添加收缩压和舒张压记录，以便前端正确显示
                        if (!systolic.equals("--")) {
                            Map<String, Object> systolicItem = new HashMap<>();
                            systolicItem.put("date", vitalTime);
                            systolicItem.put("value", systolic + " mmHg (收缩压)");
                            systolicItem.put("displayText", "体征时间：" + vitalTime + " | 登记时间：" + registerTime + " | 数值：" + systolic + " mmHg (收缩压)");
                            resultList.add(systolicItem);
                        }

                        if (!diastolic.equals("--")) {
                            Map<String, Object> diastolicItem = new HashMap<>();
                            diastolicItem.put("date", vitalTime);
                            diastolicItem.put("value", diastolic + " mmHg (舒张压)");
                            diastolicItem.put("displayText", "体征时间：" + vitalTime + " | 登记时间：" + registerTime + " | 数值：" + diastolic + " mmHg (舒张压)");
                            resultList.add(diastolicItem);
                        }
                    }

                } catch (Exception e) {
                    // 数据解析失败时跳过该记录
                    continue;
                }
            }

        } catch (Exception e) {
            // 查询异常时返回空列表
            return new ArrayList<>();
        }

        return resultList;
    }

    /**
     * 获取血糖数据 - 按日期和时间段分组，保留每天每个时间段的最后一条记录
     */
    private List<Map<String, Object>> getBloodSugarData(String patientId) {
        List<Map<String, Object>> resultList = new ArrayList<>();

        try {
            // 查询血糖记录
            LambdaQueryWrapper<FatSignRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FatSignRecord::getSignName, "血糖");
            wrapper.eq(FatSignRecord::getPatientId, patientId);
            wrapper.orderByDesc(FatSignRecord::getSignTime);

            List<FatSignRecord> bloodSugarRecords = fatSignRecordService.list(wrapper);

            // 按日期+时间段分组，保留每天每个时间段的最后一条记录
            Map<String, FatSignRecord> groupedData = new LinkedHashMap<>();
            for (FatSignRecord record : bloodSugarRecords) {
                String dateKey = DateUtils.parseDateToStr("yyyy-MM-dd", record.getSignTime());
                String timeSlot = record.getTimeSlot();
                String groupKey = dateKey + "_" + timeSlot;

                // 如果该日期+时间段还没有记录，或者当前记录的时间更晚，则保留当前记录
                if (!groupedData.containsKey(groupKey) ||
                    record.getSignTime().after(groupedData.get(groupKey).getSignTime())) {
                    groupedData.put(groupKey, record);
                }
            }

            // 按日期倒序排列
            List<String> sortedKeys = new ArrayList<>(groupedData.keySet());
            sortedKeys.sort((a, b) -> {
                String dateA = a.split("_")[0];
                String dateB = b.split("_")[0];
                int dateCompare = dateB.compareTo(dateA); // 日期倒序
                if (dateCompare != 0) return dateCompare;
                // 同一天内按时间段排序
                String timeSlotA = a.split("_")[1];
                String timeSlotB = b.split("_")[1];
                return timeSlotA.compareTo(timeSlotB);
            });

            for (String groupKey : sortedKeys) {
                FatSignRecord record = groupedData.get(groupKey);
                try {
                    // 解析form_value字段
                    JSONObject formData = JSONObject.parseObject(record.getFormValue());
                    String signTime = formData.getString("signTime");
                    String beforeValue = formData.getString("before");
                    String afterValue = formData.getString("after");

                    // 获取时间段信息
                    String timeSlot = getTimeSlotDisplayName(record.getTimeSlot());

                    String registerTime = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm", record.getCreateTime() != null ? record.getCreateTime() : record.getSignTime());
                    String vitalTime = signTime != null && signTime.contains(":") ? signTime : DateUtils.parseDateToStr("yyyy-MM-dd HH:mm", record.getSignTime());

                    // 如果有餐前血糖值
                    if (beforeValue != null && !beforeValue.isEmpty() && !"0".equals(beforeValue)) {
                        Map<String, Object> beforeItem = new HashMap<>();
                        beforeItem.put("date", vitalTime);
                        beforeItem.put("timeSlot", timeSlot + "餐前");
                        beforeItem.put("value", beforeValue + " mmol/L (" + timeSlot + "餐前)");
                        beforeItem.put("displayText", "体征时间：" + vitalTime + " | 登记时间：" + registerTime + " | 数值：" + beforeValue + " mmol/L (" + timeSlot + "餐前)");
                        resultList.add(beforeItem);
                    }

                    // 如果有餐后血糖值
                    if (afterValue != null && !afterValue.isEmpty() && !"0".equals(afterValue)) {
                        Map<String, Object> afterItem = new HashMap<>();
                        afterItem.put("date", vitalTime);
                        afterItem.put("timeSlot", timeSlot + "餐后");
                        afterItem.put("value", afterValue + " mmol/L (" + timeSlot + "餐后)");
                        afterItem.put("displayText", "体征时间：" + vitalTime + " | 登记时间：" + registerTime + " | 数值：" + afterValue + " mmol/L (" + timeSlot + "餐后)");
                        resultList.add(afterItem);
                    }

                } catch (Exception e) {
                    // 数据解析失败时跳过该记录
                    continue;
                }
            }

        } catch (Exception e) {
            // 查询异常时返回空列表
            return new ArrayList<>();
        }

        return resultList;
    }

    /**
     * 获取时间段显示名称
     */
    private String getTimeSlotDisplayName(String timeSlot) {
        if (timeSlot == null) {
            return "未知时段";
        }

        switch (timeSlot) {
            case "早上":
                return "早";
            case "中午":
                return "午";
            case "晚上":
                return "晚";
            default:
                return timeSlot;
        }
    }

    /**
     * 后台体征报告查看图表数据
     *
     * @param signTime
     * @param signType
     * @return
     */
    @GetMapping("/getChartData")
    public AjaxResult getChartData(String signTime, String signType, String patientId) {
        Map<String, String> typeValuemap = new HashMap<>();
        typeValuemap.put("体重", "weight");
        typeValuemap.put("腰围", "waist");
        String[] split = signTime.split("~");
        if ("体重".equals(signType) || "腰围".equals(signType)) {
            LambdaQueryWrapper<FatSignRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FatSignRecord::getSignName, signType);
            wrapper.between(FatSignRecord::getSignTime, split[0], split[1] + " 23:59:59");
            wrapper.eq(FatSignRecord::getPatientId, patientId);
            wrapper.orderByAsc(FatSignRecord::getSignTime);
            JSONArray array = new JSONArray();
            List<FatSignRecord> list = fatSignRecordService.list(wrapper);
            for (FatSignRecord fatSignRecord : list) {
                String formValue = fatSignRecord.getFormValue();
                JSONObject jsonObject = JSONObject.parseObject(formValue);
                array.add(jsonObject);
            }
            Map<String, Object> map = new HashMap<>();
            List<String> months = new ArrayList<>();
            List<BigDecimal> values = new ArrayList<>();
            for (Object o : array) {
                JSONObject jsonObject = (JSONObject) o;
                months.add(jsonObject.getString("signTime"));
                values.add(jsonObject.getBigDecimal(typeValuemap.get(signType)));
            }
            JSONObject data = new JSONObject();
            data.put(signType + "变化趋势", values);
            StringBuffer sb = new StringBuffer();
            sb.append("共" + list.size() + "条数据.");
            values.stream().max(BigDecimal::compareTo).ifPresent(max -> sb.append("最高值:" + max + "."));
            values.stream().min(BigDecimal::compareTo).ifPresent(min -> sb.append("最低值:" + min + "."));
            map.put("remark", sb.toString());
            map.put("months", months);
            map.put("values", data);
            return success(map);
        }
        if ("运动".equals(signType)) {
            LambdaQueryWrapper<FatPatientSport> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FatPatientSport::getPatientId, patientId);
            wrapper.between(FatPatientSport::getSportDate, split[0], split[1] + " 23:59:59");
            wrapper.orderByAsc(FatPatientSport::getSportDate);
            List<FatPatientSport> list = patientSportService.list(wrapper);
            Map<String, Object> resultMap = new HashMap<>();
            if (list.size() > 0) {
                TreeMap<Date, List<FatPatientSport>> map = new TreeMap<>();
                for (FatPatientSport fatPatientSport : list) {
                    List<FatPatientSport> fatPatientSports = map.get(fatPatientSport.getSportDate());
                    if (fatPatientSports != null) {
                        fatPatientSports.add(fatPatientSport);
                    } else {
                        fatPatientSports = new ArrayList<>();
                        fatPatientSports.add(fatPatientSport);
                        map.put(fatPatientSport.getSportDate(), fatPatientSports);
                    }
                }
                Set<Date> dates = map.keySet();
                List<String> months = new ArrayList<>();
                List<BigDecimal> values = new ArrayList<>();
                for (Date date : dates) {
                    String s = DateUtils.dateTime(date);
                    months.add(s);
                    List<FatPatientSport> fatPatientSports = map.get(date);
                    BigDecimal reduce = fatPatientSports.stream().map(item -> item.getEnergy()).reduce(BigDecimal.ZERO,
                            BigDecimal::add);
                    values.add(reduce);
                }
                StringBuffer sb = new StringBuffer();
                sb.append("共" + list.size() + "条数据.");
                values.stream().max(BigDecimal::compareTo).ifPresent(max -> sb.append("最高值:" + max + "."));
                values.stream().min(BigDecimal::compareTo).ifPresent(min -> sb.append("最低值:" + min + "."));
                JSONObject data = new JSONObject();
                data.put("运动消耗能量", values);
                resultMap.put("remark", sb.toString());
                resultMap.put("months", months);
                resultMap.put("values", data);
                return success(resultMap);
            }
        }
        if ("饮食".equals(signType)) {
            LambdaQueryWrapper<FatEat> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FatEat::getPatientId, patientId);
            wrapper.between(FatEat::getEatTime, split[0], split[1] + " 23:59:59");
            wrapper.orderByAsc(FatEat::getEatTime);
            List<FatEat> list = eatService.list(wrapper);
            Map<String, Object> resultMap = new HashMap<>();
            if (list != null && list.size() > 0) {
                TreeMap<Date, List<FatEat>> map = new TreeMap<>();
                for (FatEat fatEat : list) {
                    List<FatEat> eats = map.get(fatEat.getEatTime());
                    if (eats != null) {
                        eats.add(fatEat);
                    } else {
                        eats = new ArrayList<>();
                        eats.add(fatEat);
                        map.put(fatEat.getEatTime(), eats);
                    }
                }
                Set<Date> dates = map.keySet();
                List<String> months = new ArrayList<>();
                List<BigDecimal> values = new ArrayList<>();
                for (Date date : dates) {
                    String s = DateUtils.dateTime(date);
                    months.add(s);
                    List<FatEat> fatPatientSports = map.get(date);
                    LambdaQueryWrapper<FatEatItem> itemLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    itemLambdaQueryWrapper.in(FatEatItem::getEatId,
                            fatPatientSports.stream().map(FatEat::getId).collect(Collectors.toList()));
                    List<FatEatItem> eatItems = eatItemService.list(itemLambdaQueryWrapper);
                    BigDecimal reduce = eatItems.stream().map(FatEatItem::getEnergy).reduce(BigDecimal.ZERO,
                            BigDecimal::add);
                    values.add(reduce);
                }
                StringBuffer sb = new StringBuffer();
                sb.append("共" + list.size() + "条数据.");
                values.stream().max(BigDecimal::compareTo).ifPresent(max -> sb.append("最高值:" + max + "."));
                values.stream().min(BigDecimal::compareTo).ifPresent(min -> sb.append("最低值:" + min + "."));

                JSONObject data = new JSONObject();
                data.put("饮食摄入能量", values);
                resultMap.put("remark", sb.toString());
                resultMap.put("months", months);
                resultMap.put("values", data);
                return success(resultMap);
            }
        }
        return success();
    }

    @GetMapping("/SignList")
    public TableDataInfo SignList(FatSignRecord fatSignRecord) {

        List<FatSignRecord> list = fatSignRecordService.selectFatSignRecordList(fatSignRecord);
        return getDataTable(list);
    }

    /**
     * 微信小程序查询图表数据
     *
     * @param signTime
     * @param signType
     * @param during
     * @return
     */
    @GetMapping("/getSignChartData")
    public AjaxResult getSignChartData(String signTime, String signType, String during, String patientId) {
        CaszPatient patient;
        if (patientId != null && !"undefined".equals(patientId)) {
            patient = caszPatientService.getById(patientId);
        } else {
            patient = caszPatientService.getBySysUserId(getUserId());
        }
        Map<String, String> typeValuemap = new HashMap<>();
        typeValuemap.put("体重", "weight");
        typeValuemap.put("腰围", "waist");
        if ("体重".equals(signType)) {
            if ("day".equals(during)) {
                Map<String, Object> resultMap = new HashMap<>();
                List<Double> values = new ArrayList<>();
                List<String> months = new ArrayList<>();
                LambdaQueryWrapper<FatSignRecord> wrapper = new LambdaQueryWrapper<>();

                wrapper.eq(FatSignRecord::getSignName, signType);
                wrapper.le(FatSignRecord::getSignTime, signTime);
                wrapper.eq(FatSignRecord::getIdNo, patient.getIdNo());
                wrapper.orderByDesc(FatSignRecord::getSignTime); // 先按时间倒序，获取最新的记录
                wrapper.last("limit 7"); // 获取最近7条记录

                List<FatSignRecord> list = fatSignRecordService.list(wrapper);

                // 将结果按时间正序排序
                list.sort((a, b) -> a.getSignTime().compareTo(b.getSignTime()));

                String i = "weight";

                for (FatSignRecord record : list) {
                    String date = new SimpleDateFormat("MM-dd").format(record.getSignTime());
                    // 获取getTimeSlot() 并取前两位
//                    String timeSlot = record.getTimeSlot().substring(0, Math.min(2, record.getTimeSlot().length()));
                    // 先注释掉拼接内容，防止底部过于拥挤
                    // String formattedDate = date + "(" + timeSlot + ")";
                    String formattedDate = date;
                    try {
                        JSONObject jsonObject = JSONObject.parseObject(record.getFormValue());
                        double weight = jsonObject.getDouble(i);

                        months.add(formattedDate);
                        values.add(weight);
                    } catch (Exception e) {
                        e.printStackTrace();
                        // 处理解析错误，这里不添加到列表中
                    }
                }

                resultMap.put("months", months);
                resultMap.put("values", values); // 存储原始值

                // 返回结果
                return success(resultMap);
            }
            if ("week".equals(during) || "month".equals(during)) {
                Map<String, Object> map = new HashMap<>();
                List<String> months = new ArrayList<>();
                List<String> values = new ArrayList<>();
                for (int i = 5; i >= 0; i--) {
                    Map<String, String> recent5WeeksStartEnd;
                    if ("week".equals(during)) {
                        recent5WeeksStartEnd = DateUtils.getRecent5WeeksStartEnd(signTime, i);
                    } else {
                        recent5WeeksStartEnd = DateUtils.getRecentMonthsStartEnd(signTime, i);
                    }
                    LambdaQueryWrapper<FatSignRecord> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(FatSignRecord::getSignName, signType);
                    wrapper.eq(FatSignRecord::getIdNo, patient.getIdNo());
                    wrapper.between(FatSignRecord::getSignTime, recent5WeeksStartEnd.get("f"),
                            recent5WeeksStartEnd.get("l"));
                    List<FatSignRecord> list = fatSignRecordService.list(wrapper);
                    for (FatSignRecord record : list) {
                        String date = DateUtils.parseDateToStr(DateUtils.MM_DD, record.getSignTime());
                        // 获取getTimeSlot() 并取前两位
                        String timeSlot = record.getTimeSlot().substring(0, Math.min(2, record.getTimeSlot().length()));
                        // 先注释掉拼接内容，防止底部过于拥挤
                        // String formattedDate = date + "(" + timeSlot + ")";
                        String formattedDate = date;

                        months.add(formattedDate);
                        String formValue = record.getFormValue();
                        JSONObject jsonObject = JSONObject.parseObject(formValue);
                        values.add(jsonObject.getString("weight"));
                    }
                }

                map.put("months", months);
                map.put("values", values);
                return success(map);
            }
        }
        if ("腰围".equals(signType)) {
            if ("day".equals(during)) {
                Map<String, Object> resultMap = new HashMap<>();
                List<Double> values = new ArrayList<>();
                List<String> months = new ArrayList<>();
                LambdaQueryWrapper<FatSignRecord> wrapper = new LambdaQueryWrapper<>();

                wrapper.eq(FatSignRecord::getSignName, signType);
                wrapper.le(FatSignRecord::getSignTime, signTime);
                wrapper.eq(FatSignRecord::getIdNo, patient.getIdNo());
                wrapper.orderByDesc(FatSignRecord::getSignTime); // 先按时间倒序，获取最新的记录
                wrapper.last("limit 7"); // 获取最近7条记录

                List<FatSignRecord> list = fatSignRecordService.list(wrapper);

                // 将结果按时间正序排序
                list.sort((a, b) -> a.getSignTime().compareTo(b.getSignTime()));

                String i = "";
                if ("体重".equals(signType)) {
                    i = "weight";
                } else {
                    i = "waist";
                }

                Map<String, List<Double>> weightsByDate = new HashMap<>(); // 临时存储每天的体重列表，用于计算平均值
                for (FatSignRecord record : list) {
                    String date = new SimpleDateFormat("MM-dd").format(record.getSignTime());
                    try {
                        JSONObject jsonObject = JSONObject.parseObject(record.getFormValue());
                        double weight = jsonObject.getDouble(i);

                        // 将体重添加到对应日期的列表中
                        weightsByDate.computeIfAbsent(date, k -> new ArrayList<>()).add(weight);

                        // 如果这是新的一天（即map中之前没有这个日期），则添加到日期列表中
                        if (!months.contains(date)) {
                            months.add(date);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        // 处理解析错误，这里不添加到列表中
                    }
                }

                // 计算平均值并添加到结果中
                for (String date : months) {
                    List<Double> weights = weightsByDate.get(date);
                    double average = weights.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                    values.add(DoubleListUtils.formatAverage(average));
                }

                resultMap.put("months", months);
                resultMap.put("values", values); // 存储平均值而不是原始值

                // 返回结果
                return success(resultMap);
            }
            if ("week".equals(during) || "month".equals(during)) {
                Map<String, Object> map = new HashMap<>();
                List<String> months = new ArrayList<>();
                List<String> values = new ArrayList<>();
                for (int i = 5; i >= 0; i--) {
                    Map<String, String> recent5WeeksStartEnd;
                    if ("week".equals(during)) {
                        recent5WeeksStartEnd = DateUtils.getRecent5WeeksStartEnd(signTime, i);
                    } else {
                        recent5WeeksStartEnd = DateUtils.getRecentMonthsStartEnd(signTime, i);
                    }
                    LambdaQueryWrapper<FatSignRecord> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(FatSignRecord::getSignName, signType);
                    wrapper.eq(FatSignRecord::getIdNo, patient.getIdNo());
                    wrapper.between(FatSignRecord::getSignTime, recent5WeeksStartEnd.get("f"),
                            recent5WeeksStartEnd.get("l"));
                    List<FatSignRecord> list = fatSignRecordService.list(wrapper);
                    if (list.size() == 1) {
                        months.add(DateUtils.parseDateToStr(DateUtils.MM_DD, list.get(0).getSignTime()));
                        String formValue = list.get(0).getFormValue();
                        JSONObject jsonObject = JSONObject.parseObject(formValue);
                        values.add(jsonObject.getString(typeValuemap.get(signType)));
                    } else {
                        if (list.size() > 1) {
                            months.add(recent5WeeksStartEnd.get("f").substring(5));
                            OptionalDouble weight2 = list.stream().map(item -> {
                                String formValue = item.getFormValue();
                                JSONObject jsonObject = JSONObject.parseObject(formValue);
                                Double weight = jsonObject.getDouble(typeValuemap.get(signType));
                                return weight;
                            }).mapToDouble(Double::doubleValue).average();

                            if (weight2.isPresent()) {
                                String format = String.format("%.2f", weight2.getAsDouble());
                                values.add(format);
                            } else {
                                values.add("0");
                            }
                        }
                    }
                }

                map.put("months", months);
                map.put("values", values);
                return success(map);
            }
        }
        if ("运动".equals(signType)) {
            LambdaQueryWrapper<FatPatientSport> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FatPatientSport::getIdNo, patient.getIdNo());
            wrapper.ge(FatPatientSport::getSportDate, DateUtils.getDatePastDate(new Date(), 7));
            wrapper.orderByAsc(FatPatientSport::getSportDate);
            List<FatPatientSport> list = patientSportService.list(wrapper);
            Map<String, Object> resultMap = new HashMap<>();
            if (list.size() > 0) {
                TreeMap<Date, List<FatPatientSport>> map = new TreeMap<>();
                for (FatPatientSport fatPatientSport : list) {
                    List<FatPatientSport> fatPatientSports = map.get(fatPatientSport.getSportDate());
                    if (fatPatientSports != null) {
                        fatPatientSports.add(fatPatientSport);
                    } else {
                        fatPatientSports = new ArrayList<>();
                        fatPatientSports.add(fatPatientSport);
                        map.put(fatPatientSport.getSportDate(), fatPatientSports);
                    }
                }
                Set<Date> dates = map.keySet();
                List<String> months = new ArrayList<>();
                List<BigDecimal> values = new ArrayList<>();
                for (Date date : dates) {
                    String s = DateUtils.teTi(date);
                    months.add(s);
                    List<FatPatientSport> fatPatientSports = map.get(date);
                    BigDecimal reduce = fatPatientSports.stream().map(item -> item.getEnergy()).reduce(BigDecimal.ZERO,
                            BigDecimal::add);
                    values.add(reduce);
                }
                resultMap.put("months", months);
                resultMap.put("values", values);
                return success(resultMap);
            }
        }
        return success();
    }

    /**
     * 查询体重指标数据列表
     */
    @GetMapping("/list")
    public TableDataInfo list(FatSignRecord fatSignRecord) {
        startPage();
        List<FatSignRecord> list = fatSignRecordService.selectFatSignRecordList(fatSignRecord);
        return getDataTable(list);
    }

    /**
     * 导出体重指标数据列表
     */
    // @PreAuthorize("@ss.hasPermi('sign:signRecord:export')")
    @Log(title = "体重指标数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FatSignRecord fatSignRecord) {
        List<FatSignRecord> list = fatSignRecordService.selectFatSignRecordList(fatSignRecord);
        ExcelUtil<FatSignRecord> util = new ExcelUtil<FatSignRecord>(FatSignRecord.class);
        util.exportExcel(response, list, "体重指标数据数据");
    }

    /**
     * 获取体重指标数据详细信息
     */
    // @PreAuthorize("@ss.hasPermi('sign:signRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(fatSignRecordService.selectFatSignRecordById(id));
    }

    /**
     * 获取患者某个体重数据最新一条数据
     *
     * @param signType
     * @return
     */
    @GetMapping(value = "/getNew")
    public AjaxResult getNew(String signType, String signTime) {
        LambdaQueryWrapper<FatSignRecord> wrapper = new LambdaQueryWrapper<>();
        String username = getUsername();
        if (StringUtils.isNotEmpty(username)) {
            wrapper.eq(FatSignRecord::getIdNo, username);
            wrapper.eq(FatSignRecord::getSignName, signType);
            if (StringUtils.isNotEmpty(signTime)) {
                wrapper.le(FatSignRecord::getSignTime, signTime);
            } else {
                wrapper.orderByDesc(FatSignRecord::getSignTime);
            }
            wrapper.last("limit 1");
            FatSignRecord one = fatSignRecordService.getOne(wrapper);
            if (one != null) {
                String formValue = one.getFormValue();
                return success(JSONObject.parseObject(formValue));
            }
        }
        return success();
    }

    /**
     * 获取指定日期的体重数据和计算信息（BMI和体重变化）
     *
     * @param signTime 指定日期 格式：yyyy-MM-dd
     * @return 包含当天体重、BMI、体重变化的数据
     */
    @GetMapping(value = "/getWeightDataByDate")
    public AjaxResult getWeightDataByDate(String signTime, String patientId) {
        if (StringUtils.isEmpty(signTime)) {
            return error("日期参数不能为空");
        }

        // 获取患者信息，参考getSignChartData的实现
        CaszPatient patient;
        if (patientId != null && !"undefined".equals(patientId)) {
            patient = caszPatientService.getById(patientId);
        } else {
            patient = caszPatientService.getBySysUserId(getUserId());
        }

        if (patient == null) {
            return error("未找到患者信息");
        }

        Map<String, Object> result = new HashMap<>();

        try {
            // 构造当天的开始和结束时间
            String startTime = signTime + " 00:00:00";
            String endTime = signTime + " 23:59:59";

            // 1. 获取指定日期当天的体重记录（最后一条）
            LambdaQueryWrapper<FatSignRecord> todayWrapper = new LambdaQueryWrapper<>();
            todayWrapper.eq(FatSignRecord::getIdNo, patient.getIdNo());
            todayWrapper.eq(FatSignRecord::getSignName, "体重");
            todayWrapper.between(FatSignRecord::getSignTime, startTime, endTime);
            todayWrapper.orderByDesc(FatSignRecord::getSignTime);
            todayWrapper.orderByDesc(FatSignRecord::getCreateTime);
            todayWrapper.last("limit 1");

            FatSignRecord todayRecord = fatSignRecordService.getOne(todayWrapper);

            if (todayRecord == null) {
                // 当天没有记录，返回空数据
                result.put("hasData", false);
                result.put("message", "当天没有体重记录");
                return success(result);
            }

            // 解析当天体重数据
            JSONObject todayData = JSONObject.parseObject(todayRecord.getFormValue());
            String todayWeight = todayData.getString("weight");

            result.put("hasData", true);
            result.put("todayWeight", todayWeight);
            result.put("signTime", signTime);
            result.put("timeSlot", todayRecord.getTimeSlot());

            // 2. 获取该日期之前最近一天的体重记录（用于计算体重变化）
            LambdaQueryWrapper<FatSignRecord> previousWrapper = new LambdaQueryWrapper<>();
            previousWrapper.eq(FatSignRecord::getIdNo, patient.getIdNo());
            previousWrapper.eq(FatSignRecord::getSignName, "体重");
            previousWrapper.lt(FatSignRecord::getSignTime, startTime);
            previousWrapper.orderByDesc(FatSignRecord::getSignTime);
            previousWrapper.orderByDesc(FatSignRecord::getCreateTime);
            previousWrapper.last("limit 1");

            FatSignRecord previousRecord = fatSignRecordService.getOne(previousWrapper);

            if (previousRecord != null) {
                JSONObject previousData = JSONObject.parseObject(previousRecord.getFormValue());
                String previousWeight = previousData.getString("weight");

                // 计算体重变化
                if (StringUtils.isNotEmpty(todayWeight) && StringUtils.isNotEmpty(previousWeight)) {
                    try {
                        double todayWeightValue = Double.parseDouble(todayWeight);
                        double previousWeightValue = Double.parseDouble(previousWeight);
                        double change = todayWeightValue - previousWeightValue;

                        String weightChange = String.format("%s%.2fkg",
                            change >= 0 ? "+" : "", change);

                        result.put("weightChange", weightChange);
                        result.put("previousWeight", previousWeight);
                        result.put("previousDate", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, previousRecord.getSignTime()));
                    } catch (NumberFormatException e) {
                        result.put("weightChange", "");
                    }
                }
            } else {
                result.put("weightChange", "");
                result.put("message", "没有找到之前的体重记录");
            }

        } catch (Exception e) {
            return error("查询失败：" + e.getMessage());
        }

        return success(result);
    }

    @GetMapping(value = "/getSignDataByTypeAndTime")
    public TableDataInfo getSignData(String signType, String signTime, String timeSlot) {
        LambdaQueryWrapper<FatSignRecord> wrapper = new LambdaQueryWrapper<>();
        String username = getUsername();

        CaszPatient patient;
        if (username != null && !username.isEmpty()) {
            patient = caszPatientService.getBySysUserId(getUserId());
            if (username.length() != 18) {
                username = patient.getIdNo();
            }
        }

        List<JSONObject> formValues = new ArrayList<>();

        if (StringUtils.isNotEmpty(username)) {
            wrapper.eq(FatSignRecord::getIdNo, username);
            wrapper.eq(FatSignRecord::getSignName, signType);
            wrapper.eq(FatSignRecord::getSignTime, signTime);
            wrapper.orderByAsc(FatSignRecord::getTimeSlot);
            if (StringUtils.isNotEmpty(timeSlot)) {
                wrapper.eq(FatSignRecord::getTimeSlot, timeSlot);
            }

            List<FatSignRecord> list = fatSignRecordService.list(wrapper);
            if (!list.isEmpty()) {
                formValues = list.stream()
                        .map(record -> {
                            JSONObject jsonObject = JSONObject.parseObject(record.getFormValue()); // 假设 getFormValue()
                                                                                                   // 返回的是 JSON 字符串
                            jsonObject.put("timeSlot", record.getTimeSlot()); // 添加 timeSlot 字段
                            return jsonObject;
                        })
                        .collect(Collectors.toList());
            }
        }

        // 返回包含所有 formValue 的 AjaxResult
        return getDataTable(formValues);
    }

    /**
     * 新增体重指标数据
     */
    // @PreAuthorize("@ss.hasPermi('sign:signRecord:add')")
    @Log(title = "体重指标数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FatSignRecord fatSignRecord) {
        return toAjax(fatSignRecordService.save(fatSignRecord));
    }

    @Log(title = "体重指标数据", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @Transactional
    public AjaxResult addSign(@RequestBody FatSignRecord fatSignRecord) {

        fatSignRecord.setCreateTime(new Date());
        // fatSignRecord.setUnitId(getUnitId());
        fatSignRecord.setCreateBy(getUsername());
        fatSignRecord.setStatusCd(0);
        String formValue = fatSignRecord.getFormValue();
        if (StringUtils.isNotEmpty(formValue)) {
            JSONObject object = JSONObject.parseObject(formValue);
            Date signTime = object.getDate("signTime");
            if (signTime == null) {
                signTime = DateUtils.parseDate(DateUtils.getDate());
                object.put("signTime", DateUtils.getDate());
                String string = object.toJSONString();
                fatSignRecord.setFormValue(string);
            }
            fatSignRecord.setSignTime(signTime);
        }
        LambdaQueryWrapper<FatSignRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FatSignRecord::getIdNo, fatSignRecord.getIdNo());
        wrapper.eq(FatSignRecord::getSignTime, fatSignRecord.getSignTime());
        wrapper.eq(FatSignRecord::getSignName, fatSignRecord.getSignName());
        if (fatSignRecord.getTimeSlot() != null) {
            wrapper.eq(FatSignRecord::getTimeSlot, fatSignRecord.getTimeSlot());
        }
        fatSignRecordService.remove(wrapper);

        return toAjax(fatSignRecordService.save(fatSignRecord));
    }

    /**
     * 修改体重指标数据
     */
    // @PreAuthorize("@ss.hasPermi('sign:signRecord:edit')")
    @Log(title = "体重指标数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FatSignRecord fatSignRecord) {
        return toAjax(fatSignRecordService.updateFatSignRecord(fatSignRecord));
    }

    /**
     * 删除体重指标数据
     */
    // @PreAuthorize("@ss.hasPermi('sign:signRecord:remove')")
    @Log(title = "体重指标数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(fatSignRecordService.deleteFatSignRecordByIds(ids));
    }

    /**
     * 获取趋势卡片图表数据
     * 用于首页四个趋势卡片的数据展示
     *
     * @param patientId 患者ID，可选
     * @return 返回四个指标的趋势数据
     */
    @GetMapping("/getTrendCardData")
    public AjaxResult getTrendCardData(String patientId) {
        CaszPatient patient;
        if (patientId != null && !"undefined".equals(patientId)) {
            patient = caszPatientService.getById(patientId);
        } else {
            patient = caszPatientService.getBySysUserId(getUserId());
        }

        if (patient == null) {
            return error("患者信息不存在");
        }

        Map<String, Object> resultMap = new HashMap<>();

        // 获取体重趋势数据(最近7天)
        Map<String, Object> weightData = getWeightTrendData(patient.getIdNo(), 7);
        resultMap.put("weight", weightData);

        // 获取腰围趋势数据(最近15天)
        Map<String, Object> waistData = getWaistTrendData(patient.getIdNo(), 15);
        resultMap.put("waist", waistData);

        // 获取饮食热量趋势数据(最近7天)
        Map<String, Object> dietData = getDietTrendData(patient.getIdNo(), 7);
        resultMap.put("diet", dietData);

        // 获取运动消耗趋势数据(最近7天)
        Map<String, Object> exerciseData = getExerciseTrendData(patient.getIdNo(), 7);
        resultMap.put("exercise", exerciseData);

        return success(resultMap);
    }

    /**
     * 获取体重趋势数据
     */
    private Map<String, Object> getWeightTrendData(String idNo, int days) {
        Map<String, Object> result = new HashMap<>();

        LambdaQueryWrapper<FatSignRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FatSignRecord::getSignName, "体重");
        wrapper.eq(FatSignRecord::getIdNo, idNo);
        wrapper.ge(FatSignRecord::getSignTime, DateUtils.getDatePastDate(new Date(), days));
        wrapper.orderByAsc(FatSignRecord::getSignTime);

        List<FatSignRecord> list = fatSignRecordService.list(wrapper);

        if (list.isEmpty()) {
            result.put("hasData", false);
            result.put("message", "暂无体重数据，请先记录体重信息");
            return result;
        }

        List<Double> values = new ArrayList<>();
        List<String> dates = new ArrayList<>();
        Double firstValue = null;
        Double lastValue = null;

        for (FatSignRecord record : list) {
            try {
                JSONObject jsonObject = JSONObject.parseObject(record.getFormValue());
                Double weight = jsonObject.getDouble("weight");
                if (weight != null) {
                    values.add(weight);
                    dates.add(DateUtils.parseDateToStr("MM-dd", record.getSignTime()));

                    if (firstValue == null) {
                        firstValue = weight;
                    }
                    lastValue = weight;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        result.put("hasData", true);
        result.put("values", values);
        result.put("dates", dates);

        // 计算变化情况
        if (firstValue != null && lastValue != null) {
            double change = lastValue - firstValue;
            String changeText = change > 0 ? String.format("上升%.1f斤", change * 2)
                    : String.format("下降%.1f斤", Math.abs(change) * 2);
            result.put("changeText", changeText);
        } else {
            result.put("changeText", "数据不足");
        }

        return result;
    }

    /**
     * 获取腰围趋势数据
     */
    private Map<String, Object> getWaistTrendData(String idNo, int days) {
        Map<String, Object> result = new HashMap<>();

        LambdaQueryWrapper<FatSignRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FatSignRecord::getSignName, "腰围");
        wrapper.eq(FatSignRecord::getIdNo, idNo);
        wrapper.ge(FatSignRecord::getSignTime, DateUtils.getDatePastDate(new Date(), days));
        wrapper.orderByAsc(FatSignRecord::getSignTime);

        List<FatSignRecord> list = fatSignRecordService.list(wrapper);

        if (list.isEmpty()) {
            result.put("hasData", false);
            result.put("message", "暂无腰围数据，请先记录腰围信息");
            return result;
        }

        List<Double> values = new ArrayList<>();
        List<String> dates = new ArrayList<>();
        Double firstValue = null;
        Double lastValue = null;

        for (FatSignRecord record : list) {
            try {
                JSONObject jsonObject = JSONObject.parseObject(record.getFormValue());
                Double waist = jsonObject.getDouble("waist");
                if (waist != null) {
                    values.add(waist);
                    dates.add(DateUtils.parseDateToStr("MM-dd", record.getSignTime()));

                    if (firstValue == null)
                        firstValue = waist;
                    lastValue = waist;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        result.put("hasData", true);
        result.put("values", values);
        result.put("dates", dates);

        // 计算变化情况
        if (firstValue != null && lastValue != null) {
            double change = lastValue - firstValue;
            String changeText = change > 0 ? String.format("增加%.1fcm", change)
                    : String.format("缩小%.1fcm", Math.abs(change));
            result.put("changeText", changeText);
        } else {
            result.put("changeText", "数据不足");
        }

        return result;
    }

    /**
     * 获取饮食热量趋势数据
     */
    private Map<String, Object> getDietTrendData(String idNo, int days) {
        Map<String, Object> result = new HashMap<>();

        LambdaQueryWrapper<FatEat> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FatEat::getIdNo, idNo);
        wrapper.ge(FatEat::getEatTime, DateUtils.getDatePastDate(new Date(), days));
        wrapper.orderByAsc(FatEat::getEatTime);

        List<FatEat> list = eatService.list(wrapper);

        if (list.isEmpty()) {
            result.put("hasData", false);
            result.put("message", "暂无饮食数据，请先记录饮食信息");
            return result;
        }

        // 按日期分组计算每日热量
        TreeMap<Date, List<FatEat>> dateMap = new TreeMap<>();
        for (FatEat fatEat : list) {
            Date eatDate = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-MM-dd", fatEat.getEatTime()));
            dateMap.computeIfAbsent(eatDate, k -> new ArrayList<>()).add(fatEat);
        }

        List<Double> values = new ArrayList<>();
        List<String> dates = new ArrayList<>();
        double totalCalories = 0;

        for (Map.Entry<Date, List<FatEat>> entry : dateMap.entrySet()) {
            Date date = entry.getKey();
            List<FatEat> dayEats = entry.getValue();

            // 获取当日所有饮食项目的热量总和
            LambdaQueryWrapper<FatEatItem> itemWrapper = new LambdaQueryWrapper<>();
            itemWrapper.in(FatEatItem::getEatId, dayEats.stream().map(FatEat::getId).collect(Collectors.toList()));
            List<FatEatItem> eatItems = eatItemService.list(itemWrapper);

            BigDecimal dayCalories = eatItems.stream()
                    .map(FatEatItem::getEnergy)
                    .filter(Objects::nonNull) // 💡 过滤掉 null
                    .reduce(BigDecimal.ZERO, BigDecimal::add);


            values.add(dayCalories.doubleValue());
            dates.add(DateUtils.parseDateToStr("MM-dd", date));
            totalCalories += dayCalories.doubleValue();
        }

        result.put("hasData", true);
        result.put("values", values);
        result.put("dates", dates);

        // 计算平均热量
        double avgCalories = totalCalories / values.size();
        result.put("changeText", String.format("平均%.0f大卡", avgCalories));

        return result;
    }

    /**
     * 获取运动消耗趋势数据
     */
    private Map<String, Object> getExerciseTrendData(String idNo, int days) {
        Map<String, Object> result = new HashMap<>();

        LambdaQueryWrapper<FatPatientSport> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FatPatientSport::getIdNo, idNo);
        wrapper.ge(FatPatientSport::getSportDate, DateUtils.getDatePastDate(new Date(), days));
        wrapper.orderByAsc(FatPatientSport::getSportDate);

        List<FatPatientSport> list = patientSportService.list(wrapper);

        if (list.isEmpty()) {
            result.put("hasData", false);
            result.put("message", "暂无运动数据，请先记录运动信息");
            return result;
        }

        // 按日期分组计算每日运动消耗
        TreeMap<Date, List<FatPatientSport>> dateMap = new TreeMap<>();
        for (FatPatientSport sport : list) {
            Date sportDate = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-MM-dd", sport.getSportDate()));
            dateMap.computeIfAbsent(sportDate, k -> new ArrayList<>()).add(sport);
        }

        List<Double> values = new ArrayList<>();
        List<String> dates = new ArrayList<>();
        double totalEnergy = 0;

        for (Map.Entry<Date, List<FatPatientSport>> entry : dateMap.entrySet()) {
            Date date = entry.getKey();
            List<FatPatientSport> daySports = entry.getValue();

            BigDecimal dayEnergy = daySports.stream()
                    .map(FatPatientSport::getEnergy)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            values.add(dayEnergy.doubleValue());
            dates.add(DateUtils.parseDateToStr("MM-dd", date));
            totalEnergy += dayEnergy.doubleValue();
        }

        result.put("hasData", true);
        result.put("values", values);
        result.put("dates", dates);
        result.put("changeText", String.format("总消耗%.0f大卡", totalEnergy));

        return result;
    }

    /**
     * 获取患者详情页体征图表数据
     * 用于患者详情页面6个体征图表的数据展示：体重、饮食记录、腰围、血糖、运动量、血压
     *
     * @param patientId 患者ID，可选
     * @param days      查询天数，默认30天
     * @return 返回六个指标的趋势数据
     */
    @GetMapping("/getPatientDetailChartData")
    public AjaxResult getPatientDetailChartData(String patientId, Integer days) {
        CaszPatient patient;
        if (patientId != null && !"undefined".equals(patientId)) {
            patient = caszPatientService.getById(patientId);
        } else {
            patient = caszPatientService.getBySysUserId(getUserId());
        }

        if (patient == null) {
            return error("患者信息不存在");
        }

        // 默认查询30天的数据
        if (days == null || days <= 0) {
            days = 30;
        }

        Map<String, Object> resultMap = new HashMap<>();

        // 获取体重趋势数据
        Map<String, Object> weightData = getDetailWeightTrendData(patient.getIdNo(), days);
        resultMap.put("weight", weightData);

        // 获取饮食记录趋势数据
        Map<String, Object> dietData = getDetailDietTrendData(patient.getIdNo(), days);
        resultMap.put("dietRecord", dietData);

        // 获取腰围趋势数据
        Map<String, Object> waistData = getDetailWaistTrendData(patient.getIdNo(), days);
        resultMap.put("waistCircumference", waistData);

        // 获取血糖趋势数据
        Map<String, Object> bloodSugarData = getDetailBloodSugarTrendData(patient.getIdNo(), days);
        resultMap.put("bloodSugar", bloodSugarData);

        // 获取运动量趋势数据
        Map<String, Object> exerciseData = getDetailExerciseTrendData(patient.getIdNo(), days);
        resultMap.put("exerciseAmount", exerciseData);

        // 获取血压趋势数据
        Map<String, Object> bloodPressureData = getDetailBloodPressureTrendData(patient.getIdNo(), days);
        resultMap.put("bloodPressure", bloodPressureData);

        return success(resultMap);
    }

    /**
     * 获取患者详情页体重趋势数据
     */
    private Map<String, Object> getDetailWeightTrendData(String idNo, int days) {
        Map<String, Object> result = new HashMap<>();

        LambdaQueryWrapper<FatSignRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FatSignRecord::getSignName, "体重");
        wrapper.eq(FatSignRecord::getIdNo, idNo);
        wrapper.ge(FatSignRecord::getSignTime, DateUtils.getDatePastDate(new Date(), days));
        wrapper.orderByAsc(FatSignRecord::getSignTime);

        List<FatSignRecord> list = fatSignRecordService.list(wrapper);

        List<Double> values = new ArrayList<>();
        List<String> dates = new ArrayList<>();

        for (FatSignRecord record : list) {
            try {
                JSONObject jsonObject = JSONObject.parseObject(record.getFormValue());
                Double weight = jsonObject.getDouble("weight");
                if (weight != null) {
                    values.add(weight);
                    dates.add(DateUtils.parseDateToStr("yyyy-MM-dd", record.getSignTime()));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        result.put("values", values);
        result.put("dates", dates);
        result.put("unit", "kg");
        result.put("hasData", !values.isEmpty());

        return result;
    }

    /**
     * 获取患者详情页饮食记录趋势数据
     */
    private Map<String, Object> getDetailDietTrendData(String idNo, int days) {
        Map<String, Object> result = new HashMap<>();

        LambdaQueryWrapper<FatEat> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FatEat::getIdNo, idNo);
        wrapper.ge(FatEat::getEatTime, DateUtils.getDatePastDate(new Date(), days));
        wrapper.orderByAsc(FatEat::getEatTime);

        List<FatEat> list = eatService.list(wrapper);

        // 按日期分组计算每日热量
        TreeMap<String, List<FatEat>> dateMap = new TreeMap<>();
        for (FatEat fatEat : list) {
            String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd", fatEat.getEatTime());
            dateMap.computeIfAbsent(dateStr, k -> new ArrayList<>()).add(fatEat);
        }

        List<Double> values = new ArrayList<>();
        List<String> dates = new ArrayList<>();

        for (Map.Entry<String, List<FatEat>> entry : dateMap.entrySet()) {
            String dateStr = entry.getKey();
            List<FatEat> dayEats = entry.getValue();

            // 获取当日所有饮食项目的热量总和
            LambdaQueryWrapper<FatEatItem> itemWrapper = new LambdaQueryWrapper<>();
            itemWrapper.in(FatEatItem::getEatId, dayEats.stream().map(FatEat::getId).collect(Collectors.toList()));
            List<FatEatItem> eatItems = eatItemService.list(itemWrapper);

            BigDecimal dayCalories = eatItems.stream()
                    .map(FatEatItem::getEnergy)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            values.add(dayCalories.doubleValue());
            dates.add(dateStr);
        }

        result.put("values", values);
        result.put("dates", dates);
        result.put("unit", "kcal");
        result.put("hasData", !values.isEmpty());

        return result;
    }

    /**
     * 获取患者详情页腰围趋势数据
     */
    private Map<String, Object> getDetailWaistTrendData(String idNo, int days) {
        Map<String, Object> result = new HashMap<>();

        LambdaQueryWrapper<FatSignRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FatSignRecord::getSignName, "腰围");
        wrapper.eq(FatSignRecord::getIdNo, idNo);
        wrapper.ge(FatSignRecord::getSignTime, DateUtils.getDatePastDate(new Date(), days));
        wrapper.orderByAsc(FatSignRecord::getSignTime);

        List<FatSignRecord> list = fatSignRecordService.list(wrapper);

        List<Double> values = new ArrayList<>();
        List<String> dates = new ArrayList<>();

        for (FatSignRecord record : list) {
            try {
                JSONObject jsonObject = JSONObject.parseObject(record.getFormValue());
                Double waist = jsonObject.getDouble("waist");
                if (waist != null) {
                    values.add(waist);
                    dates.add(DateUtils.parseDateToStr("yyyy-MM-dd", record.getSignTime()));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        result.put("values", values);
        result.put("dates", dates);
        result.put("unit", "cm");
        result.put("hasData", !values.isEmpty());

        return result;
    }

    /**
     * 获取患者详情页血糖趋势数据
     */
    private Map<String, Object> getDetailBloodSugarTrendData(String idNo, int days) {
        Map<String, Object> result = new HashMap<>();

        LambdaQueryWrapper<FatSignRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FatSignRecord::getSignName, "血糖");
        wrapper.eq(FatSignRecord::getIdNo, idNo);
        wrapper.ge(FatSignRecord::getSignTime, DateUtils.getDatePastDate(new Date(), days));
        wrapper.orderByAsc(FatSignRecord::getSignTime);

        List<FatSignRecord> list = fatSignRecordService.list(wrapper);

        List<Double> values = new ArrayList<>();
        List<String> dates = new ArrayList<>();

        for (FatSignRecord record : list) {
            try {
                JSONObject jsonObject = JSONObject.parseObject(record.getFormValue());
                Double bloodSugar = jsonObject.getDouble("bloodSugar");
                if (bloodSugar != null) {
                    values.add(bloodSugar);
                    dates.add(DateUtils.parseDateToStr("yyyy-MM-dd", record.getSignTime()));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        result.put("values", values);
        result.put("dates", dates);
        result.put("unit", "mmol/L");
        result.put("hasData", !values.isEmpty());

        return result;
    }

    /**
     * 获取患者详情页运动量趋势数据
     */
    private Map<String, Object> getDetailExerciseTrendData(String idNo, int days) {
        Map<String, Object> result = new HashMap<>();

        LambdaQueryWrapper<FatPatientSport> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FatPatientSport::getIdNo, idNo);
        wrapper.ge(FatPatientSport::getSportDate, DateUtils.getDatePastDate(new Date(), days));
        wrapper.orderByAsc(FatPatientSport::getSportDate);

        List<FatPatientSport> list = patientSportService.list(wrapper);

        // 按日期分组计算每日运动消耗（这里可以用步数或消耗的卡路里）
        TreeMap<String, List<FatPatientSport>> dateMap = new TreeMap<>();
        for (FatPatientSport sport : list) {
            String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd", sport.getSportDate());
            dateMap.computeIfAbsent(dateStr, k -> new ArrayList<>()).add(sport);
        }

        List<Double> values = new ArrayList<>();
        List<String> dates = new ArrayList<>();

        for (Map.Entry<String, List<FatPatientSport>> entry : dateMap.entrySet()) {
            String dateStr = entry.getKey();
            List<FatPatientSport> daySports = entry.getValue();

            // 这里使用运动消耗的总能量作为运动量指标，您也可以改为步数等其他指标
            BigDecimal dayEnergy = daySports.stream()
                    .map(FatPatientSport::getEnergy)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            values.add(dayEnergy.doubleValue());
            dates.add(dateStr);
        }

        result.put("values", values);
        result.put("dates", dates);
        result.put("unit", "kcal"); // 运动消耗单位，您可以根据需要修改为"步数"等
        result.put("hasData", !values.isEmpty());

        return result;
    }

    /**
     * 获取患者详情页血压趋势数据
     * 血压数据格式：{"signTime":"2024-08-31","ssy":"110","szy":"34"}
     * ssy: 收缩压，szy: 舒张压
     */
    private Map<String, Object> getDetailBloodPressureTrendData(String idNo, int days) {
        Map<String, Object> result = new HashMap<>();

        LambdaQueryWrapper<FatSignRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FatSignRecord::getSignName, "血压");
        wrapper.eq(FatSignRecord::getIdNo, idNo);
        wrapper.ge(FatSignRecord::getSignTime, DateUtils.getDatePastDate(new Date(), days));
        wrapper.orderByAsc(FatSignRecord::getSignTime);

        List<FatSignRecord> list = fatSignRecordService.list(wrapper);

        List<Double> systolicValues = new ArrayList<>(); // 收缩压
        List<Double> diastolicValues = new ArrayList<>(); // 舒张压
        List<String> dates = new ArrayList<>();
        List<String> bloodPressureStrings = new ArrayList<>(); // 用于前端显示的血压字符串

        for (FatSignRecord record : list) {
            try {
                JSONObject jsonObject = JSONObject.parseObject(record.getFormValue());
                Double ssy = jsonObject.getDouble("ssy"); // 收缩压
                Double szy = jsonObject.getDouble("szy"); // 舒张压

                if (ssy != null && szy != null) {
                    systolicValues.add(ssy);
                    diastolicValues.add(szy);
                    dates.add(DateUtils.parseDateToStr("yyyy-MM-dd", record.getSignTime()));
                    bloodPressureStrings.add(String.format("%.0f/%.0f", ssy, szy));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        result.put("systolicValues", systolicValues); // 收缩压数组
        result.put("diastolicValues", diastolicValues); // 舒张压数组
        result.put("bloodPressureStrings", bloodPressureStrings); // 血压字符串数组（如"120/80"）
        result.put("dates", dates);
        result.put("unit", "mmHg");
        result.put("hasData", !systolicValues.isEmpty());

        return result;
    }

    /**
     * 获取血压图表数据
     * 专门用于血压记录页面的图表展示
     *
     * @param signTime  基准日期
     * @param during    时间段：day(日)、week(周)、month(月)
     * @param patientId 患者ID，可选
     * @return 返回血压图表数据
     */
    @GetMapping("/getBloodPressureChartData")
    public AjaxResult getBloodPressureChartData(String signTime, String during, String patientId) {
        CaszPatient patient;
        if (patientId != null && !"undefined".equals(patientId)) {
            patient = caszPatientService.getById(patientId);
        } else {
            patient = caszPatientService.getBySysUserId(getUserId());
        }

        if (patient == null) {
            return error("患者信息不存在");
        }

        // 根据时间段计算查询范围
        Date baseDate;
        try {
            baseDate = DateUtils.parseDate(signTime.split(" ")[0]);
        } catch (Exception e) {
            return error("日期格式错误");
        }

        Date startDate;
        Date endDate = baseDate;
        int days;

        switch (during) {
            case "day":
                days = 7; // 显示最近7天
                break;
            case "week":
                days = 30; // 显示最近30天
                break;
            case "month":
                days = 90; // 显示最近90天
                break;
            default:
                days = 7;
        }

        startDate = DateUtils.getDatePastDate(baseDate, days);

        // 查询血压记录
        LambdaQueryWrapper<FatSignRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FatSignRecord::getSignName, "血压");
        wrapper.eq(FatSignRecord::getIdNo, patient.getIdNo());
        wrapper.between(FatSignRecord::getSignTime, startDate, endDate);
        wrapper.orderByAsc(FatSignRecord::getSignTime);

        List<FatSignRecord> list = fatSignRecordService.list(wrapper);

        // 处理数据，按日期分组取平均值
        Map<String, List<Double>> systolicByDate = new LinkedHashMap<>();
        Map<String, List<Double>> diastolicByDate = new LinkedHashMap<>();

        for (FatSignRecord record : list) {
            try {
                JSONObject jsonObject = JSONObject.parseObject(record.getFormValue());
                Double ssy = jsonObject.getDouble("ssy"); // 收缩压
                Double szy = jsonObject.getDouble("szy"); // 舒张压

                if (ssy != null && szy != null) {
                    String dateKey = DateUtils.parseDateToStr("yyyy-MM-dd", record.getSignTime());

                    systolicByDate.computeIfAbsent(dateKey, k -> new ArrayList<>()).add(ssy);
                    diastolicByDate.computeIfAbsent(dateKey, k -> new ArrayList<>()).add(szy);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 计算每日平均值并格式化数据
        List<String> months = new ArrayList<>();
        List<Double> ssyValues = new ArrayList<>();
        List<Double> szyValues = new ArrayList<>();

        for (String dateKey : systolicByDate.keySet()) {
            List<Double> dailySystolic = systolicByDate.get(dateKey);
            List<Double> dailyDiastolic = diastolicByDate.get(dateKey);

            if (!dailySystolic.isEmpty() && !dailyDiastolic.isEmpty()) {
                // 计算当日平均值
                double avgSystolic = dailySystolic.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                double avgDiastolic = dailyDiastolic.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);

                months.add(formatDateForChart(dateKey, during));
                ssyValues.add(Math.round(avgSystolic * 10.0) / 10.0); // 保留一位小数
                szyValues.add(Math.round(avgDiastolic * 10.0) / 10.0); // 保留一位小数
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("months", months);
        result.put("ssyValues", ssyValues); // 收缩压数组
        result.put("szyValues", szyValues); // 舒张压数组
        result.put("hasData", !ssyValues.isEmpty());

        return success(result);
    }

    /**
     * 根据时间段格式化日期显示
     */
    private String formatDateForChart(String dateStr, String during) {
        try {
            Date date = DateUtils.parseDate(dateStr);
            switch (during) {
                case "day":
                    return DateUtils.parseDateToStr("MM-dd", date);
                case "week":
                    return DateUtils.parseDateToStr("MM-dd", date);
                case "month":
                    return DateUtils.parseDateToStr("MM-dd", date);
                default:
                    return DateUtils.parseDateToStr("MM-dd", date);
            }
        } catch (Exception e) {
            return dateStr;
        }
    }

    /**
     * 格式化血糖图表的日期时间段显示为"7月28号[早上]"格式
     */
    private String formatDateTimeSlotForChart(String dateStr, String timeSlot) {
        try {
            Date date = DateUtils.parseDate(dateStr);
            // 格式化为"7月28号[早上]"
            String monthDay = DateUtils.parseDateToStr("M月d号", date);
            return monthDay + "[" + timeSlot + "]";
        } catch (Exception e) {
            return dateStr + "[" + timeSlot + "]";
        }
    }

    /**
     * 获取血糖图表数据
     * 专门用于血糖记录页面的图表展示
     *
     * @param signTime  基准日期
     * @param during    时间段：day(日)、week(周)、month(月)
     * @param patientId 患者ID，可选
     * @return 返回血糖图表数据
     */
    @GetMapping("/getBloodSugarChartData")
    public AjaxResult getBloodSugarChartData(String signTime, String during, String patientId) {
        CaszPatient patient;
        if (patientId != null && !"undefined".equals(patientId)) {
            patient = caszPatientService.getById(patientId);
        } else {
            patient = caszPatientService.getBySysUserId(getUserId());
        }

        if (patient == null) {
            return error("患者信息不存在");
        }

        // 根据时间段计算查询范围
        Date baseDate;
        try {
            baseDate = DateUtils.parseDate(signTime.split(" ")[0]);
        } catch (Exception e) {
            return error("日期格式错误");
        }

        Date startDate;
        Date endDate = baseDate;
        int days;

        switch (during) {
            case "day":
                days = 7; // 显示最近7天
                break;
            case "week":
                days = 30; // 显示最近30天
                break;
            case "month":
                days = 90; // 显示最近90天
                break;
            default:
                days = 7;
        }

        startDate = DateUtils.getDatePastDate(baseDate, days);

        // 查询血糖记录
        LambdaQueryWrapper<FatSignRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FatSignRecord::getSignName, "血糖");
        wrapper.eq(FatSignRecord::getIdNo, patient.getIdNo());
        wrapper.between(FatSignRecord::getSignTime, startDate, endDate);
        wrapper.orderByAsc(FatSignRecord::getSignTime);

        List<FatSignRecord> list = fatSignRecordService.list(wrapper);

        // 处理数据，按日期和时间段分组，保存最后一次记录
        Map<String, Map<String, FatSignRecord>> latestRecordByDateAndTime = new LinkedHashMap<>();

        for (FatSignRecord record : list) {
            try {
                JSONObject jsonObject = JSONObject.parseObject(record.getFormValue());
                Double before = jsonObject.getDouble("before"); // 餐前血糖
                Double after = jsonObject.getDouble("after");   // 餐后血糖
                String timeSlot = record.getTimeSlot(); // 早上、中午、晚上

                if (before != null && after != null && timeSlot != null) {
                    String dateKey = DateUtils.parseDateToStr("yyyy-MM-dd", record.getSignTime());

                    // 保存每个日期每个时间段的最后一次记录
                    Map<String, FatSignRecord> timeSlotMap = latestRecordByDateAndTime.computeIfAbsent(dateKey, k -> new HashMap<>());

                    // 如果该日期该时间段还没有记录，或者当前记录时间更晚，则更新
                    FatSignRecord existingRecord = timeSlotMap.get(timeSlot);
                    if (existingRecord == null || record.getSignTime().after(existingRecord.getSignTime())) {
                        timeSlotMap.put(timeSlot, record);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 按时间段分别显示数据，格式化为"7月28号[早上]"
        List<String> months = new ArrayList<>();
        List<Double> beforeValues = new ArrayList<>();
        List<Double> afterValues = new ArrayList<>();

        // 获取所有有数据的日期并排序
        Set<String> allDates = new TreeSet<>(latestRecordByDateAndTime.keySet());

        for (String dateKey : allDates) {
            Map<String, FatSignRecord> timeSlotRecords = latestRecordByDateAndTime.get(dateKey);

            if (timeSlotRecords != null && !timeSlotRecords.isEmpty()) {
                // 遍历早上、中午、晚上三个时间段，每个时间段单独显示
                for (String timeSlot : Arrays.asList("早上", "中午", "晚上")) {
                    FatSignRecord record = timeSlotRecords.get(timeSlot);
                    if (record != null) {
                        try {
                            JSONObject jsonObject = JSONObject.parseObject(record.getFormValue());
                            Double before = jsonObject.getDouble("before");
                            Double after = jsonObject.getDouble("after");

                            if (before != null && after != null) {
                                // 格式化X轴标签为"7月28号[早上]"
                                String formattedLabel = formatDateTimeSlotForChart(dateKey, timeSlot);

                                months.add(formattedLabel);
                                beforeValues.add(Math.round(before * 10.0) / 10.0); // 保留一位小数
                                afterValues.add(Math.round(after * 10.0) / 10.0);   // 保留一位小数
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("months", months);
        result.put("beforeValues", beforeValues); // 餐前血糖数组
        result.put("afterValues", afterValues);   // 餐后血糖数组
        result.put("hasData", !beforeValues.isEmpty());

        return success(result);
    }

    /**
     * 获取患者AI聊天历史记录
     * 用于患者详情页面AI助手的历史对话记录
     *
     * @param patientId 患者ID
     * @return 返回聊天历史记录列表
     */
    @GetMapping("/getPatientAiChatHistory")
    public AjaxResult getPatientAiChatHistory(String patientId) {
        try {
            if (StringUtils.isEmpty(patientId)) {
                return error("患者ID不能为空");
            }

            String userId = getUserId().toString();

            // 获取历史对话记录（获取最近20条记录）
            AssistantRecord multiwheel = new AssistantRecord();
            multiwheel.setSendId(userId);
            multiwheel.setRecvId("AI_SYSTEM");
            List<AssistantRecord> multiwheelList = assistantRecordService.selectmultiwheelList(multiwheel);

            // 转换为前端需要的格式
            List<Map<String, Object>> chatHistory = new ArrayList<>();
            for (AssistantRecord record : multiwheelList) {
                // 只添加有效的问题和回答
                if (StringUtils.isNotEmpty(record.getProblem())) {
                    Map<String, Object> chatItem = new HashMap<>();
                    chatItem.put("id", record.getId());
                    chatItem.put("content", record.getProblem());
                    chatItem.put("isUser", true);
                    chatItem.put("timestamp", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", record.getCreateTime()));
                    chatHistory.add(chatItem);
                }

                // 添加AI回复
                if (StringUtils.isNotEmpty(record.getAnswer())) {
                    Map<String, Object> aiReply = new HashMap<>();
                    aiReply.put("id", record.getId() + "_reply");
                    aiReply.put("content", record.getAnswer());
                    aiReply.put("isUser", false);
                    aiReply.put("timestamp", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", record.getCreateTime()));
                    chatHistory.add(aiReply);
                }
            }

            // 按时间排序（最新的在前面）
            chatHistory.sort((a, b) -> {
                String timeA = (String) a.get("timestamp");
                String timeB = (String) b.get("timestamp");
                try {
                    Date dateA = DateUtils.parseDate(timeA);
                    Date dateB = DateUtils.parseDate(timeB);
                    return dateB.compareTo(dateA);
                } catch (Exception e) {
                    return 0;
                }
            });

            return success(chatHistory);

        } catch (Exception e) {
            e.printStackTrace();
            return error("获取聊天历史记录失败: " + e.getMessage());
        }
    }

    /**
     * 患者详情页AI助手对话接口
     * 支持上下文记忆的AI对话功能
     *
     * @param request 包含患者ID和对话内容的请求
     * @return AI回复内容
     */
    @PostMapping("/patientAiChat")
    public AjaxResult patientAiChat(@RequestBody Map<String, Object> request) {
        try {
            String patientId = (String) request.get("patientId");
            String message = (String) request.get("message");
            String userId = getUserId().toString();

            if (StringUtils.isEmpty(patientId) || StringUtils.isEmpty(message)) {
                return error("患者ID和消息内容不能为空");
            }

            // 获取患者信息
            CaszPatient patient = caszPatientService.getById(patientId);
            if (patient == null) {
                return error("患者信息不存在");
            }

            // 构建AI消息对象
            Map<String, Object> textElem = new HashMap<>();
            textElem.put("content", message);

            AIMessage aiMessage = new AIMessage();
            aiMessage.setSendID(userId);
            aiMessage.setRecvID("AI_SYSTEM");
            aiMessage.setPatientId(patientId);
            aiMessage.setTextElem(textElem);
            aiMessage.setContentType(101); // 文本消息
            aiMessage.setSessionType(1); // 单聊
            aiMessage.setShowName("患者");
            aiMessage.setSenderFaceURL("");

            // 检查用户是否被禁言
            ImProhibit jinProhibit = new ImProhibit();
            jinProhibit.setProhibitTime(new Date());
            List<ImProhibit> imProhibitList = imProhibitService.selectImProhibitList(jinProhibit);
            if (!imProhibitList.isEmpty()) {
                return error("抱歉，您违反社区规定次数过多，本功能暂停使用");
            }

            // 检查今日使用次数
            AssistantRecord assistantRecordToday = new AssistantRecord();
            assistantRecordToday.setSendId(userId);
            assistantRecordToday.setCreateTime(new Date());
            List<AssistantRecord> recordTodayList = assistantRecordService.selectTodayList(assistantRecordToday);
            if (recordTodayList.size() >= 30) {
                return error("抱歉，您今日的提问次数已用尽");
            }

            // 获取历史对话记录（保持上下文）
            AssistantRecord multiwheel = new AssistantRecord();
            multiwheel.setSendId(userId);
            multiwheel.setRecvId("AI_SYSTEM");
            List<AssistantRecord> multiwheelList = assistantRecordService.selectmultiwheelList(multiwheel);

            // 调用AI服务获取回复
            String content = aiUtils.selectToolMessage(multiwheelList, message, patientId, "","");
            content = content.replace("\n", " <br>");

            // 记录对话
            AssistantRecord assistantRecord = new AssistantRecord();
            assistantRecord.setSendId(userId);
            assistantRecord.setRecvId("AI_SYSTEM");
            assistantRecord.setProblem(message);
            assistantRecord.setAnswer(content);
            assistantRecord.setCreateTime(new Date());
            if (content.contains("无法回答您")) {
                assistantRecord.setViolation("1");
            }
            assistantRecordService.save(assistantRecord);

            // 检查违规次数
            AssistantRecord ass = new AssistantRecord();
            ass.setViolation("1");
            ass.setSendId(userId);
            List<AssistantRecord> assistantRecordList = assistantRecordService.selectAssistantRecordList(ass);

            if (assistantRecordList.size() >= 3) {
                // 设置禁言
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DAY_OF_YEAR, 1);
                Date newDate = calendar.getTime();
                ImProhibit imProhibit = new ImProhibit();
                imProhibit.setImId(userId);
                imProhibit.setProhibitTime(newDate);
                imProhibitService.save(imProhibit);
                return error("抱歉，您违反社区规定次数过多，本功能暂停使用");
            }

            // 返回AI回复
            Map<String, Object> result = new HashMap<>();
            result.put("reply", content);
            result.put("timestamp", new Date());

            return success(result);

        } catch (Exception e) {
            e.printStackTrace();
            return error("AI服务暂时不可用，请稍后重试");
        }
    }

    /**
     * 医生专用AI助手对话接口
     * 支持外部传入主要问题提示词（sysPrompt）+患者上下文
     *
     * @param request 包含患者ID、对话内容、主要问题提示词的请求
     * @return AI回复内容
     */
    @PostMapping("/doctorAiChat")
    public AjaxResult doctorAiChat(@RequestBody Map<String, Object> request) {
        try {
            String patientId = (String) request.get("patientId");
            String message = (String) request.get("message");
            String sysPrompt = (String) request.get("sysPrompt");
            String userId = getUserId().toString();

            if (StringUtils.isEmpty(patientId) || StringUtils.isEmpty(message)) {
                return error("患者ID和消息内容不能为空");
            }

            // 获取患者信息
            CaszPatient patient = caszPatientService.getById(patientId);
            if (patient == null) {
                return error("患者信息不存在");
            }

            // 获取历史对话记录（保持上下文）
            AssistantRecord multiwheel = new AssistantRecord();
            multiwheel.setSendId(userId+'_'+patientId);
            multiwheel.setRecvId("AI_SYSTEM");
            List<AssistantRecord> multiwheelList = assistantRecordService.selectmultiwheelList(multiwheel);

            // 调用新版AI服务获取回复（支持sysPrompt）
            String content = aiUtils.assistWithPatientContext(multiwheelList, message, patientId, "", sysPrompt);
            content = content.replace("\n", " <br>");

            // 记录对话
            AssistantRecord assistantRecord = new AssistantRecord();
            assistantRecord.setSendId(userId+'_'+patientId);
            assistantRecord.setRecvId("AI_SYSTEM");
            assistantRecord.setProblem(message);
            assistantRecord.setAnswer(content);
            assistantRecord.setCreateTime(new Date());
            if (content.contains("无法回答您")) {
                assistantRecord.setViolation("1");
            }
            assistantRecordService.save(assistantRecord);

            // 返回AI回复
            Map<String, Object> result = new HashMap<>();
            result.put("reply", content);
            result.put("timestamp", new Date());

            return success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return error("AI服务暂时不可用，请稍后重试");
        }
    }



    /**
     * 获取患者AI聊天历史记录
     * 用于患者详情页面AI助手的历史对话记录
     *
     * @param patientId 患者ID
     * @return 返回聊天历史记录列表
     */
    @GetMapping("/getPatientAiChatHistory2")
    public AjaxResult getPatientAiChatHistory2(String patientId) {
        try {
            if (StringUtils.isEmpty(patientId)) {
                return error("患者ID不能为空");
            }

            String userId = getUserId().toString();

            // 获取历史对话记录（获取最近20条记录）
            AssistantRecord multiwheel = new AssistantRecord();
            multiwheel.setSendId(userId+'_'+patientId);
            multiwheel.setRecvId("AI_SYSTEM");
            List<AssistantRecord> multiwheelList = assistantRecordService.selectmultiwheelList(multiwheel);

            // 转换为前端需要的格式
            List<Map<String, Object>> chatHistory = new ArrayList<>();
            for (AssistantRecord record : multiwheelList) {
                // 只添加有效的问题和回答
                if (StringUtils.isNotEmpty(record.getProblem())) {
                    Map<String, Object> chatItem = new HashMap<>();
                    chatItem.put("id", record.getId());
                    chatItem.put("content", record.getProblem());
                    chatItem.put("isUser", true);
                    chatItem.put("timestamp", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", record.getCreateTime()));
                    chatHistory.add(chatItem);
                }

                // 添加AI回复
                if (StringUtils.isNotEmpty(record.getAnswer())) {
                    Map<String, Object> aiReply = new HashMap<>();
                    aiReply.put("id", record.getId() + "_reply");
                    aiReply.put("content", record.getAnswer());
                    aiReply.put("isUser", false);
                    aiReply.put("timestamp", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", record.getCreateTime()));
                    chatHistory.add(aiReply);
                }
            }

            // 按时间排序（最新的在前面）
            chatHistory.sort((a, b) -> {
                String timeA = (String) a.get("timestamp");
                String timeB = (String) b.get("timestamp");
                try {
                    Date dateA = DateUtils.parseDate(timeA);
                    Date dateB = DateUtils.parseDate(timeB);
                    return dateB.compareTo(dateA);
                } catch (Exception e) {
                    return 0;
                }
            });

            return success(chatHistory);

        } catch (Exception e) {
            e.printStackTrace();
            return error("获取聊天历史记录失败: " + e.getMessage());
        }
    }

}
