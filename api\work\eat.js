import request from '@/config/request.js';

//获取用户当前餐别的提交记录
export const getUserFoodItems = (params)=>request.get("/sign/eatItem/userItems",params,{custom:{isBgApi:true}})

//保存饮食单项
export const saveFoodItem = (params)=>request.post("/sign/eatItem/addItem",params,{custom:{isBgApi:true}})
//删除饮食单项
export const delFoodItem = (params)=>request.post("/sign/eatItem/del/"+params,params,{custom:{isBgApi:true}})

//保存就餐记录
export const saveEat = (params)=>request.post("/sign/eat/addEat",params,{custom:{isBgApi:true}})

//查询患者减重干预指定表单
export const getUserCurIntervene = (params)=>request.get("/intervene/intervene/userCur",params,{custom:{isBgApi:true}})

//查询患者某一天的就餐记录
export const getUserEatList = (params)=>request.get("/sign/eat/userList",params,{custom:{isBgApi:true}})

//分页查询患者饮食记录，倒序
export const getUserPageEatList = (params)=>request.get("/sign/eat/userPageList",params,{custom:{isBgApi:true}})


export const getEatChartData = (params)=>request.get("/sign/eatItem/getEatChartData",params,{custom:{isBgApi:true}})

//查询AI识别录入的食物详情（从fat_eat_item表获取）
export const getAIFoodItemDetail = (params)=>request.get("/sign/eatItem/detail/"+params,params,{custom:{isBgApi:true}})

//获取有饮食记录的日期列表
export const getRecordedDates = (params)=>request.get("/sign/eat/recordedDates",params,{custom:{isBgApi:true}})

