<template>
  <view class="mobile-item-container">
    <Navbar :hideBtn="false" title="问题编辑" bgColor="#b7bbfa" :h5Show="false" :fixed="true" :placeholder="false" :titleStyle="{color:'#FFFFFF', fontSize:'16px', fontWeight:'500'}"></Navbar>
    <view class="form-container">
      <u--form ref="noticeForm" :model="notice" :rules="rules" labelPosition="left">
     <!-- <u-form-item label="标题" prop="noticeTitle" borderBottom>
        <u--textarea v-model="notice.noticeTitle" placeholder="请输入标题" :count="false" :maxlength="30" :autoHeight="true" confirmType="done"></u--textarea>
      </u-form-item> -->
      <u-form-item label="类型" prop="questionType" borderBottom @click="actionShow = true;">
        <u--input v-model="questionTypeName" disabled disabledColor="#ffffff" placeholder="请选择类型" border="none"></u--input>
        <u-icon slot="right" name="arrow-right"></u-icon>
      </u-form-item>
     <!-- <u-form-item label="状态" prop="status" borderBottom>
        <u-radio-group v-model="notice.status">
          <u-radio shape="circle" label="正常" name="0" checked></u-radio>
          <u-radio shape="circle" label="关闭" name="1"></u-radio>
        </u-radio-group>
      </u-form-item> -->
      <u-form-item label="正文" prop="questContent">
        <u--textarea
          v-model="notice.questContent"
          placeholder="请输入问题内容"
          :count="true"
          :maxlength="600"
          :autoHeight="true"
          height="200"
          confirmType="done"
        ></u--textarea>
      </u-form-item>
    </u--form>
    <u-action-sheet :actions="actions" :title="actionTitle" :show="actionShow" @close="actionShow = false" @select="actionSelect"></u-action-sheet>
    <view class="button-container">
      <u-button v-if="noticeId" type="error" text="删除" @click="del" class="delete-btn"></u-button>
      <u-button color="#4f41ac" text="提交" @click="submit" class="submit-btn"></u-button>
    </view>
    </view>
  </view>
</template>

<script>
import * as UnitApi from "@/api/work/unit.js"
import * as NoticeApi from '@/api/work/notice'
import * as QuestionApi from "@/api/questions.js"
import Navbar from '@/components/navbar/Navbar'

export default {
  components: {
    Navbar,
  },
  data () {
    return {
      noticeId: undefined,
	  unit:{},
	  unitId: null,
      notice: {
        noticeTitle: '',
        statusCd: '1',
        questContent: '',
		unitId:'',
      },
      actionShow: false,
      actions: [{
        name: '用药方面',
        value: '1'
      }, {
        name: '饮食方面',
        value: '2'
      }, {
        name: '心理方面',
        value: '3'
      }, {
        name: '运动方面',
        value: '4'
      }, {
        name: '其他',
        value: '5'
      }],
      actionTitle: '',
      questionTypeName: null,
      rules: {
        // noticeTitle: [ { required: true, message: '请输入公告标题', trigger: ['blur', 'change'] } ],
        questionType: [ { required: true, message: '请选择问题类型', trigger: ['blur', 'change'] } ],
        // status: [ { required: true, message: '请选择公告状态', trigger: ['blur', 'change'] } ],
        questContent: [ { required: true, message: '请输入问题', trigger: ['blur', 'change'] } ],
      }
    }
  },
  onLoad (params) {
    // this.noticeId = params.id
	 this.noticeId = params.id
	this.notice.unitId = this.$store.state.unit.id
    this.loadData()
  },
  // onLoad(option) {
  // 	console.log("option",option)
  // 	if (option && option.scene) {
  // 		this.unitId = option.scene
  // 		this.getUnitInfo()
  // 	}
  // },
  methods: {
    loadData () {
      if (this.noticeId) {
        const app = this
        NoticeApi.noticeById(this.noticeId).then(res => {
          app.notice = res.data
        })
      }
    },
    del () {
      NoticeApi.noticeDelete(this.noticeId).then(res => {
        uni.showToast({ title: '保存成功！' })
      })
    },
    submit () {
      this.$refs.noticeForm.validate().then(res => {
        if (this.noticeId) {
          NoticeApi.noticeModify(this.notice).then(res => {
            uni.showToast({ title: '提交成功！' })
          })
        } else {

		  QuestionApi.save(this.notice).then(res => {
            uni.showToast({ title: '提交成功！' })
			console.log("unit",this.notice)
			this.goBack()
          })
        }
      });
    },
    actionSelect (item) {
      this.questionTypeName = item.name;
      this.notice.questionType = item.value;
      this.$refs.noticeForm.validateField('questionType');
    },
	getUnitInfo() {
		UnitApi.unit(this.unitId).then(res => {
			if (res.code == 200) {
				this.unit = res.data
				console.log("加载unit",res.data)
			}
		})
	},
    goBack () {
      uni.navigateBack({ delta: 1});
    }
  }
}
</script>

<style lang="scss" scoped>
.mobile-item-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.form-container {
  /* 增加顶部间距，避免与导航栏重叠 */
  padding: 80rpx 40rpx 40rpx 40rpx;
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 统一表单项样式 */
/deep/ .u-form-item {
  margin-bottom: 32rpx;
}

/deep/ .u-form-item .u-form-item__body {
  padding: 32rpx 0 !important;
  border-bottom: 1px solid #f0f0f0;
  align-items: flex-start;
}

/deep/ .u-form-item .u-form-item__body__left {
  width: 140rpx !important;
  margin-right: 24rpx;
  padding-top: 8rpx;
  flex-shrink: 0;
}

/deep/ .u-form-item .u-form-item__body__left__content__label {
  font-size: 32rpx !important;
  color: #333333 !important;
  font-weight: 500;
  line-height: 1.4;
}

/deep/ .u-form-item .u-form-item__body__right {
  flex: 1;
  display: flex;
  align-items: center;
  width: calc(100% - 164rpx);
}

/* 修复uView组件内层容器宽度 */
/deep/ .u-form-item__body__right__content {
  width: 100% !important;
  flex: 1 !important;
}

/deep/ .u-form-item__body__right__content__slot {
  width: 100% !important;
  flex: 1 !important;
}

/* 文本域表单项特殊处理 - 最后一个表单项 */
/deep/ .u-form-item:last-child .u-form-item__body {
  align-items: flex-start;
  min-height: 240rpx;
}

/deep/ .u-form-item:last-child .u-form-item__body__right {
  align-items: flex-start;
  width: calc(100% - 164rpx) !important;
  min-height: 200rpx;
  flex: 1 !important;
}

/* 特别针对文本域的容器宽度修复 */
/deep/ .u-form-item:last-child .u-form-item__body__right__content {
  width: 100% !important;
  flex: 1 !important;
}

/deep/ .u-form-item:last-child .u-form-item__body__right__content__slot {
  width: 100% !important;
  flex: 1 !important;
}

/* 输入框样式统一 */
/deep/ .u-input__content__field-wrapper__field {
  font-size: 32rpx !important;
  color: #666666;
  text-align: left;
}

/deep/ .u-textarea__content__field {
  font-size: 32rpx !important;
  color: #666666;
  line-height: 1.5;
  min-height: 200rpx !important;
  height: 200rpx !important;
  width: 100% !important;
  box-sizing: border-box;
}

/deep/ .u-textarea__content {
  width: 100% !important;
  min-height: 200rpx !important;
  background-color: #fafafa;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  padding: 16rpx;
  box-sizing: border-box;
}

/deep/ .u-textarea {
  width: 100% !important;
  min-height: 200rpx !important;
  flex: 1 !important;
}

/* 全面修复所有可能限制宽度的容器 */
/deep/ .u-input__content,
/deep/ .u-textarea__content,
/deep/ .u-input,
/deep/ .u-textarea,
/deep/ .u-form-item__body__right > *,
/deep/ .u-form-item__body__right__content > *,
/deep/ .u-form-item__body__right__content__slot > * {
  width: 100% !important;
  max-width: none !important;
  flex: 1 !important;
  box-sizing: border-box;
}

/* 禁用状态的输入框样式 */
/deep/ .u-input--disabled .u-input__content__field-wrapper__field {
  color: #666666 !important;
  background-color: transparent !important;
}

/* 选择器样式 */
/deep/ .u-icon {
  color: #999999;
}

/* 按钮容器样式 */
.button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 32rpx;
  margin-top: 60rpx;
  padding: 0 40rpx;
}

.delete-btn {
  width: 160rpx !important;
}

.submit-btn {
  width: 200rpx !important;
}

/* 确保导航栏在最顶层 */
/deep/ .u-navbar--fixed {
  z-index: 99999 !important;
}
</style>