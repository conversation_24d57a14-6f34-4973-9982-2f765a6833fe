<template>
	<view>
		<view class="index-tabs" :style="{top:tabsTop}">
			<tabs :tabData="tabsList" @tabClick="changeTabs" :activeIndex="tabsIndex"></tabs>
		</view>
		<!-- 患者信息 -->
		<view v-if="tabsIndex==0" class="contain">
			<view class="action-container">
				<view v-for="(action, index) in actions" :key="index" class="action-item"
					@click="selectTag(action.name)">
					{{ action.name }}
				</view>
			</view>
			<u--form labelPosition="top" labelWidth="120" :model="patientInfo" :rules="rules" ref="uForm">
				<!-- 基础信息 -->
				<view class="fenge" v-show="actionsName === '基础信息'">
					<u-form-item label="身份证" :padding="customPadding"  prop="patientInfo.idNo" borderBottom>
						<span>{{ patientInfo.idNo?patientInfo.idNo:'' }}</span>
					</u-form-item>
					<u-form-item label="姓名" :padding="customPadding"  prop="patientInfo.name" borderBottom>
						<span>{{ patientInfo.name ?patientInfo.name:'' }}</span>
					</u-form-item>
					<u-form-item label="手机号" :padding="customPadding"  prop="patientInfo.selfMobile" borderBottom>
						<span>{{ patientInfo.selfMobile?patientInfo.selfMobile:'' }}</span>
					</u-form-item>
					<u-form-item label="性别" :padding="customPadding"  prop="patientInfo.params.center.gender" borderBottom>
						<u--text type="primary" :text="patientInfo.params.center.gender==1?'女':'男'"></u--text>
					</u-form-item>
					<u-form-item label="平台ID号" :padding="customPadding"  prop="patientId" borderBottom>
						<span>{{ patientId?patientId:'' }}</span>
					</u-form-item>
					<u-form-item v-if="$hasAnyRoles('hos,doctor')" :padding="customPadding"  label="标签" prop="patientInfo.tags"
						@click="showTag = true" borderBottom>
						<div class="tag-container">
							<div v-for="(tag, index) in formattedTags" :key="index" class="tag-wrapper"
								@click.stop="deleteTag(tag, index)">
								<span class="tag-text">{{ tag?tag:'' }}</span>

								<u-icon name="close" size="20"></u-icon>
							</div>
						</div>
					</u-form-item>
					<u-form-item v-if="$hasAnyRoles('hos,doctor')" label="患者备注" prop="patientInfo.params.center.remark"
						borderBottom>
						<textarea @blur="saveRemark" v-model="patientInfo.params.center.remark"
							auto-height="true"></textarea>
					</u-form-item>
				</view>
				<!-- 基础体征 -->
				<view class="fenge" v-show="actionsName==='基础体征'">
					<u-form-item label="身高(cm)" :padding="customPadding"  prop="patientInfo.params.center.height" borderBottom>
						<span>{{ patientInfo.params.center.height?patientInfo.params.center.height:'' }}</span>
					</u-form-item>
					<u-form-item label="体重(kg)":padding="customPadding"   prop="patientInfo.params.center.weight" borderBottom>
						<span>{{ patientInfo.params.center.weight?patientInfo.params.center.weight:'' }}</span>
					</u-form-item>
					<u-form-item label="BMI" :padding="customPadding"  prop="BMI" borderBottom>
						<span>{{ BMI?BMI:'' }}</span>
					</u-form-item>
					<u-form-item label="BMR" :padding="customPadding"  prop="BMR" borderBottom>
						<span>{{ BMR?BMR:'' }}</span>
					</u-form-item>
					<u-form-item label="TEE":padding="customPadding"   prop="TEE" borderBottom>
						<span>{{ TEE?TEE:'' }}</span>
					</u-form-item>
					<u-form-item label="建议摄入热量":padding="customPadding"   prop="HOT" borderBottom>
						<span>{{ HOT?HOT:'' }}</span>
					</u-form-item>

					<u-form-item label="是否吸烟" :padding="customPadding"  prop="patientInfo.params.center.smokeStatus" borderBottom>
						<u--text type="primary" :text="patientInfo.params.center.smokeStatus==2?'否':'是'"></u--text>
					</u-form-item>
					<u-form-item label="是否饮酒" :padding="customPadding"  prop="patientInfo.params.center.drinkStatus" borderBottom>
						<u--text type="primary" :text="getDrinkStatusText(patientInfo.params.center.drinkStatus)"></u--text>
					</u-form-item>
				</view>
				<!-- 基本病史 -->
				<view class="fenge" v-show="actionsName==='基本病史'">
					<u-form-item label="现有疾病":padding="customPadding"   prop="patientInfo.params.center.otherDiease" borderBottom>
						<span>{{ patientInfo.params.center.otherDiease?patientInfo.params.center.otherDiease:'' }}</span>
					</u-form-item>
					<u-form-item label="家族史" :padding="customPadding"  prop="patientInfo.params.center.familyHis" borderBottom>
						<span>{{ patientInfo.params.center.familyHis?patientInfo.params.center.familyHis:'' }}</span>
					</u-form-item>
          <u-form-item v-if="$hasAnyRoles('hos,doctor')" :padding="customPadding"  label="过敏史"
                       prop="patientInfo.params.center.allergicHis" borderBottom>
						<textarea @blur="saveRemark" v-model="patientInfo.params.center.allergicHis"
                      auto-height="true"></textarea>
          </u-form-item>
					<u-form-item v-if="$hasAnyRoles('hos,doctor')" :padding="customPadding"  label="既往史"
						prop="patientInfo.params.center.pastHistory" borderBottom>
						<textarea @blur="saveRemark" v-model="patientInfo.params.center.pastHistory"
							auto-height="true"></textarea>
					</u-form-item>

					<u-form-item v-if="$hasAnyRoles('hos,doctor')" :padding="customPadding"  label="药物史"
						prop="patientInfo.params.center.drugHistory" borderBottom>
						<textarea @blur="saveRemark" v-model="patientInfo.params.center.drugHistory"
							auto-height="true"></textarea>
					</u-form-item>
				</view>
			</u--form>

			<u-action-sheet :show="showTag" :actions="tagList" title="标签" description="请选择标签" @close="showTag = false"
				@select="tagSelect">
			</u-action-sheet>
		</view>
		<!-- 就诊报告 -->
		<view v-if="tabsIndex==1" class="contain">
			<view v-if="visits && visits.length>0">
				<view class="swipe-action u-border-top u-border-bottom" v-for="(item, index) in visits" :key="index">
					<view class="swipe-action__content">
						<uni-card :title="item.createTime" :sub-title="item.idNo" :extra="item.patientName"
							:thumbnail="avatar">
							<text class="uni-body">
								{{ item.content ? item.content  : '' }}
							</text>
							<view
								style="margin: 10px 0;display: flex;flex-direction: row;justify-content: space-around;">
								<view>身高：{{ item.height ? item.height + 'cm' : '' }}</view>
								<view>体重：{{ item.weight ? item.weight + 'kg' : '' }}</view>
							</view>

							<view v-if="item.params.pics.length > 0" class="u-album-container"
								style="display: flex; justify-content: flex-end; margin-top: 20px;">
								<u-album multipleSize="70" singleMode="aspectFill" singleSize="70"
									:urls="item.params.pics" keyName="url"></u-album>
							</view>
							<!-- 添加展开折叠按钮 -->
							<view
								style="display: flex; justify-content: space-between; align-items: center; color: #3c9cff; cursor: pointer;">
								<span style="flex: 1;"></span>
								<u-icon :name="expanded[index] ? 'arrow-up' : 'arrow-down'" size="20"
									@click="toggleExpand(index)"></u-icon>
							</view>

							<view v-if="expanded[index]">
								<view style="text-align: center; font-weight: bold; font-size: 20px; margin: 20px 0;">
									门诊总结</view>
								<!-- 动态展示 advice 内容，添加序号 -->
								<view v-for="(section, sectionIndex) in formattedAdvice[index]" :key="sectionIndex">
									<view style="font-weight: bold; font-size: 19px; margin: 10px 0;">
										{{ sectionIndex + 1 }}. {{ section.title }}
									</view>
									<view v-for="(point, pointIndex) in section.points" :key="pointIndex">
										<view style="font-size: 17px; font-weight: bold; margin: 10px 0;">
											{{ point.title }}
										</view>
										<view style="text-indent: 2em;">{{ point.content }}</view>
									</view>
								</view>
							</view>
						</uni-card>
					</view>

				</view>
			</view>
			<u-empty v-else></u-empty>
		</view>

		<!-- 干预方案 -->
		<view v-if="tabsIndex==2" class="ganyucontain">
			<view v-if="inteData " style="margin: 10px 0;">
				<!-- <uni-card v-for="(item, index) in sections" :key="index" :title="item.title">
					<template #title>
						<view class="ganyutitle">{{ item.title }}</view>
					</template>
					<pre class="content">{{ item.content }}</pre>
				</uni-card> -->

				<uni-card v-for="(val,key, index) in inteReport" :key="index" :title="key">
					<template #title>
						<view class="ganyutitle">{{ key }}</view>
					</template>
					<!-- <pre class="content">{{ val }}</pre> -->
					<text class="content">{{ val }}</text>
				</uni-card>

			</view>
			<u-empty v-else> </u-empty>
		</view>


		<!-- 阶段评估 -->
		<view v-if="tabsIndex==3" class="contain">
			<view v-if="evas && evas.length>0">
				<view class="contain-assess" v-for="item in evas" :key="item.id">
					<view class="contain-assess-head">
						<view><text class="contain-assess-head-date">{{item.createTime?item.createTime:''}}</text>
						</view>
						<u-icon name="file-text" size="30"></u-icon>
					</view>
					<view class="contain-assess-content">
						{{item.content?item.content:''}}
					</view>
				</view>
			</view>
			<u-empty v-else> </u-empty>
		</view>

		<!-- 饮食分析 -->
		<view v-if="tabsIndex==4" class="contain">
			<u-tabs :list="mainNutrition" :scrollable="false" @click="tabClickMain"></u-tabs>
			<uni-card :shadow="true" title="能量 kcal">
				<template v-slot:title>
					<view style="height: 20px;line-height: 50px;display: flex;">
						<view class="uni-card-title">{{mainActive.name?mainActive.name:''}} <text
								class="font--grey indent5 font12">
								kcal</text></view>
						<view class="font--grey font12" style='flex:1;text-align: right;'>
							<text style="color:#4f41ac">--</text>实际摄入量
						</view>
						<view class="font--grey font12" style='flex:1;text-align: right;'>
							<text style="color:#8b5fdc">--</text>推荐摄入量
						</view>
					</view>
				</template>
				<view class="charts-box" v-if="mainChartData">
					<qiun-data-charts type="line" :opts="mainOpts" :chartData="mainChartData" />
				</view>
				<view style="display: flex;justify-content: space-around;margin-top: 10px;">
					<view :class="['data-range',item.id===mainDuring&&'data-range--active']" v-for="item in duringList"
						:key="item.id" @click="changeMainDuring(item.id)">
						{{item.name?item.name:''}}
					</view>
				</view>
			</uni-card>

			<u-tabs :list="otherNutrition" :scrollable="false" @click="tabClickOther"></u-tabs>
			<uni-card :shadow="true" title="能量 kcal">
				<template v-slot:title>
					<view style="height: 20px;line-height: 50px;display: flex;">
						<view class="uni-card-title">{{otherActive.name?otherActive.name:''}} <text
								class="font--grey indent5 font12">
								mg</text></view>
						<view class="font--grey font12" style='flex:1;text-align: right;'>
							<text style="color:#4f41ac">--</text>实际摄入量
						</view>
						<view class="font--grey font12" style='flex:1;text-align: right;'>
							<text style="color:#8b5fdc">--</text>推荐摄入量
						</view>
					</view>
				</template>
				<view class="charts-box">
					<qiun-data-charts type="line" :opts="otherOpts" :chartData="otherChartData" />
				</view>

				<view style="display: flex;justify-content: space-around;margin-top: 10px;">
					<view :class="['data-range',item.id===otherDuring&&'data-range--active']" v-for="item in duringList"
						:key="item.id" @click="changeOtherDuring(item.id)">
						{{item.name?item.name:''}}
					</view>

				</view>
			</uni-card>
		</view>

		<!-- 体征分析 -->
		<view v-if="tabsIndex==5" style="min-height: 100vh;" class="contain">
			<view class="word-weight ">
				<view class="word-weight-title">
					体重
				</view>
				<view class="word-weight-unit">
					单位：kg
				</view>
				<view class="word-weight-charts" v-if="sportChartData">
					<qiun-data-charts type="line" :canvas2d="true" canvasId="gJnYwKizsuIePhEMnSdUFJJApdsyzDjt"
						:activeType="hollow" :chartData="weightChartData" />
				</view>
				<u-empty v-else></u-empty>
			</view>


			<view class="word-weight">
				<view class="word-weight-title">
					运动耗能
				</view>
				<view class="word-weight-unit">
					单位：kcal
				</view>
				<view class="word-weight-charts" v-if="sportChartData">
					<qiun-data-charts type="line" :canvas2d="true" canvasId="sportChajhffddrtData" :activeType="hollow"
						:chartData="sportChartData" />
				</view>
				<u-empty v-else></u-empty>
			</view>

			<view class="word-weight">
				<view class="word-weight-title">
					腰围
				</view>
				<view class="word-weight-unit">
					单位：cm
				</view>

				<view class="word-weight-charts" v-if="waistChartData">

					<qiun-data-charts type="line" :canvas2d="true" canvasId="waistChasdrfghfrtData" :activeType="hollow"
						:chartData="waistChartData" />
				</view>
				<u-empty v-else></u-empty>
			</view>

		</view>

		<!-- 预警记录 -->
		<view v-if="tabsIndex==6" class="contain">
			<u-list v-if="warnRecords && warnRecords.length>0" @scrolltolower="warnScrollToLower">
				<view class="contain-assess" v-for="item in warnRecords" :key="item.id">
					<view class="contain-assess-head">
						<view><text class="contain-assess-head-date">{{item.createTime?item.createTime:''}}</text>
						</view>
						<u-icon :name="item.dealStatus!=0?'checkmark-circle':'edit-pen'" size="30"
							@click="editWarnRecord(item)"></u-icon>
					</view>
					<view class="contain-assess-content">
						{{item.content?item.content:''}}
					</view>

					<view v-if="item.dealRemark" class="contain-assess-content">
						{{item.dealRemark?item.dealRemark:''}}
					</view>
				</view>
			</u-list>
			<u-empty v-else></u-empty>
		</view>

		<view v-if="tabsIndex==7" class="contain">
			<u-list v-if="eatList && eatList.length>0" @scrolltolower="eatscrolltolower">
				<uni-card v-for="userEat in eatList" :key="userEat.id" v-if="userEat.eatIf=='Y'"
					:title="eat[userEat.eatType]+'（'+userEat.eatTime.substring(0,10)+'）'"
					:extra="jiSuanEnergy(userEat)">
					<view v-for="item in userEat.fatEatItemList" :key="item.id"
						v-if="userEat.fatEatItemList && userEat.fatEatItemList.length>0">
						<view v-if="item.dataType==0" @click="editItem(item)"
							style="display: flex;vertical-align: middle;justify-content: space-between;align-items: center;margin: 8px 0;">
							<view style="flex: 1;">
								<u-avatar shape="square" size="60" :src="foodImg(item.foodImg)"></u-avatar>
							</view>
							<view style="flex: 5;margin-left:8px;display: flex;flex-direction: column;">
								<view>{{item.foodName?item.foodName:''}}</view>
								<u-gap height="8"></u-gap>
								<view>{{item.weight?item.weight:''}}g/{{item.energy?item.energy:''}}kcal</view>
							</view>
							<view style="flex: 1;text-align: right;">
								<uni-icons type="right" color="#999" size="20"></uni-icons>
							</view>
						</view>
						<view v-else style="width: 100%;height: 250px;display: flex;">
							<view style="flex:7">
								<image style="width: 200px;" mode="aspectFill" :src="foodImg(item.eatImg)">
									<image>
							</view>
							<view style="flex:3;margin: auto;">
								<u-icon name="close-circle" style="margin-right:30px;"></u-icon>
							</view>
						</view>
					</view>
				</uni-card>
			</u-list>
			<u-empty v-else></u-empty>
		</view>

		<u-modal :show="warnEditShow" title="预警处理" :showCancelButton="true" @confirm="confirmEdit"
			@cancel="warnEditShow=false">
			<view class="slot-content" style="width: 100%;">
				<u--form labelPosition="top" labelWidth="100" :model="warnForm" :rules="warnRules" ref="warnForm">
					<u-form-item label="姓名" prop="warnForm.name" borderBottom>
						<u--input v-model="warnForm.name" disabled border="none"></u--input>
					</u-form-item>
					<u-form-item label="预警内容" prop="warnForm.content" borderBottom>
						<u--textarea v-model="warnForm.content" autoHeight disabled placeholder="请输入内容"></u--textarea>
					</u-form-item>
					<u-form-item label="处理" prop="warnForm.dealRemark" borderBottom>
						<u--textarea v-model="warnForm.dealRemark" placeholder="请输入处理备注"></u--textarea>
					</u-form-item>
					<u-form-item labelPosition="left" labelWidth="130" label="处理同步发送胖友" prop="warnForm.oper"
						borderBottom>
						<u-switch v-model="warnForm.oper" activeValue="im" inactiveValue="" @change="change"></u-switch>
					</u-form-item>

				</u--form>
			</view>
		</u-modal>

	</view>
</template>

<script>
	import * as UserApi from "@/api/user.js"
	import * as PatientApi from "@/api/patient.js"
	import * as TagApi from "@/api/tag.js"
	import * as EatApi from "@/api/work/eat.js"
	import * as HealthApi from "@/api/work/health.js"
	import * as SignApi from "@/api/signCommit.js"
	import * as WarnApi from "@/api/work/warn.js"
	import dayjs from 'dayjs'
	import appConfig from "@/common/config"
	import Tabs from '@/pageWork/components/wx-slideringMenu.vue'
	export default {
		components: {
			Tabs
		},
		data() {
			return {
				eat: ['早餐', '早加', '午餐', '午加', '晚餐', '晚加'],
				eatList: [],
				eatQueryParams: {
					pageNum: 1,
					pageSize: 20,
					patientId: null,
				},
				eatTotal: 0,
				customPadding: '7px 7px 2px 7px',
				expanded: [],
				// 格式化后的 advice 数据，改为数组
				formattedAdvice: [],
				//预警数据
				warnRules: [],
				warnForm: {
					id: '',
					name: '',
					content: '',
					dealRemark: '',
					oper: '',
					patientId: ''
				},
				sections: [{
						title: "1.生活方式",
						content: ""
					},
					{
						title: "2.饮食方案",
						content: ""
					},
					{
						title: "3.运动计划",
						content: ""
					},
					{
						title: "4.行为管理",
						content: ""
					},
					{
						title: "5.其他注意事项",
						content: ""
					}
				],
				inteReport:{},
				actions: [{
						name: '基础信息',
					},
					{
						name: '基础体征',
					},
					{
						name: '基本病史',
					}
				],
				actionsName: '基础信息',
				warnEditShow: false,
				warnRecords: [],
				warnQueryParams: {
					pageNum: 1,
					pageSize: 20,
					patientId: null,
				},
				warnTotal: 0,
				showTag: false,
				//体征数据
				during: 'day',
				waistChartData: null,
				sportChartData: null,
				weightChartData: null,
				signOpts: {
					padding: [20, 20, 20, 20],
					xAxis: {
						boundaryGap: 'justify',
						fontColor: '#999'
					},
					yAxis: {
						disabled: true,
						disableGrid: true
					},
					dataPointShapeType: 'hollow',
					legend: {
						show: false
					}
				},

				//饮食分析
				duringList: [{
					name: '日',
					id: 'day'
				}, {
					name: '周',
					id: 'week'
				}, {
					name: '月',
					id: 'month'
				}],
				mainDuring: 'day',
				otherDuring: 'day',
				mainNutrition: [{
					name: '能量',
					type: 'energy'
				}, {
					name: '蛋白质',
					type: 'protein'
				}, {
					name: '脂肪',
					type: 'fat'
				}, {
					name: '碳水',
					type: 'ch2o'
				}],
				mainActive: {},
				otherNutrition: [{
					name: '磷',
					type: 'pp'
				}, {
					name: '钠',
					type: 'na'
				}, {
					name: '钾',
					type: 'kk'
				}, {
					name: '钙',
					type: 'ca'
				}],
				otherActive: {},
				mainChartData: null,
				otherChartData: {},
				//您可以通过修改 config-ucharts.js 文件中下标为 ['line'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
				mainOpts: {
					color: ["#FD9C90", "#58CA8A", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: [15, 5, 0, 5],
					enableScroll: false,
					legend: {
						show: false
					},
					xAxis: {
						disableGrid: true,

					},
					yAxis: {
						disabled: true,
						gridType: "dash",
						disableGrid: true,
						dashLength: 2
					},
					extra: {
						line: {
							type: "straight",
							width: 2,
							activeType: "hollow"
						}
					}
				},

				otherOpts: {
					color: ["#FD9C90", "#58CA8A", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: [15, 5, 0, 5],
					enableScroll: false,
					legend: {
						show: false
					},
					xAxis: {
						disableGrid: true,

					},
					yAxis: {
						disabled: true,
						gridType: "dash",
						disableGrid: true,
						dashLength: 2
					},
					extra: {
						line: {
							type: "straight",
							width: 2,
							activeType: "hollow"
						}
					}
				},

				//就诊报告
				visits: [],
				formattedTagIds: [],
				tagList: null,
				BMR: 0,
				TEE: 0,
				HOT: 0,
				age: 0,
				BMI: 0,
				//定期评估
				evas: [],
				medNutri: {},
				//健康干预数据
				inteData: null,
				patientInfo: {},
				patientId: null,
				tabsList: [{
					name: '患者信息'
				}, {
					name: '就诊报告'
				}, {
					name: '干预方案'
				}, {
					name: '阶段评估'
				}, {
					name: '饮食分析'
				}, {
					name: '体征分析'
				}, {
					name: '预警记录'
				}, {
					name: '饮食记录'
				}],
				tabsIndex: 0
			}
		},

		onLoad(option) {
			console.log(option)
			if (option.userID) {
				UserApi.getImUserByUserID(option.userID).then(res => {
					console.log("imuser", res)
					if (res.code == 200) {
						this.patientId = res.data.patientId
						UserApi.getInfoByPatientId(this.patientId).then(res => {
							if (res.code == 200) {
								this.patientInfo = res.data
							}
						})
					}
				})

			} else if (option.groupID) {
				UserApi.getPatientGroupByGroupId(option.groupID).then(res => {
					console.log("patientGroup", res)
					if (res.code == 200) {
						this.patientId = res.data.patientId
						UserApi.getInfoByPatientId(this.patientId).then(res => {
							if (res.code == 200) {
								this.patientInfo = res.data
							}
						})
					}
				})
			} else if (option.patientId) {
				this.patientId = option.patientId
				UserApi.getInfoByPatientId(this.patientId).then(res => {
					if (res.code == 200) {
						this.patientInfo = res.data
						let gender = this.patientInfo.gender;
						let weight = this.patientInfo.params.center.weight;
						let height = this.patientInfo.params.center.height / 100;
						this.BMI = this.patientInfo.params.center.bmi
						console.log('BMI', this.BMI)
						let age = this.patientInfo.age;
						if (gender == '0') {
							if (age >= 18 && age <= 30) {
								this.BMR = (15.057 * weight + 692.2).toFixed(2)
								this.TEE = (this.BMR * 1.3).toFixed(2)
								this.HOT = (this.TEE - 500).toFixed(2)

							} else if (age >= 31 && age <= 60) {
								this.BMR = (11.472 * weight + 873.1).toFixed(2)
								this.TEE = (this.BMR * 1.3).toFixed(2)
								this.HOT = (this.TEE - 500).toFixed(2)

							} else if (age > 60) {
								this.BMR = (11.711 * weight + 587.7).toFixed(2)
								this.TEE = (this.BMR * 1.3).toFixed(2)
								this.HOT = (this.TEE - 500).toFixed(2)
							}

						} else if (gender == '1') {
							if (age >= 18 && age <= 30) {
								this.BMR = (14.818 * weight + 486.6).toFixed(2)
								this.TEE = (this.BMR * 1.3).toFixed(2)
								this.HOT = (this.TEE - 500).toFixed(2)
							} else if (age >= 31 && age <= 60) {
								this.BMR = (8.126 * weight + 845.6).toFixed(2)
								this.TEE = (this.BMR * 1.3).toFixed(2)
								this.HOT = (this.TEE - 500).toFixed(2)
							} else if (age > 60) {
								this.BMR = (9.082 * weight + 658.5).toFixed(2)
								this.TEE = (this.BMR * 1.3).toFixed(2)
								this.HOT = (this.TEE - 500).toFixed(2)
							}
						}
					}
				})
			}
			this.getTagList()
		},
		onReady() {
			//onReady 为uni-app支持的生命周期之一
			this.$refs.warnForm.setRules(this.warnRules)
		},
		computed: {
			formattedTags() {
				if (this.patientInfo && typeof this.patientInfo.tags === 'string') {
					this.formattedTagIds = this.patientInfo.tagIds.split(',').map(tagId => tagId.trim()).filter(tagId =>
						tagId);
					console.log("this.formattedTagIds")
					return this.patientInfo.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
				}
				return [];
			},
			tabsTop() {
				return '0px'
			},
			foodGuideData() {
				let data = {};
				if (this.inteData && this.inteData.forms && this.inteData.forms.length > 0) {
					let forms = this.inteData.forms;
					let aimForm = forms.find(item => item.formKey === 'food_guide');
					if (aimForm) {
						let json = JSON.parse(aimForm.formValue);
						data['1'] = json['guide'];
						data['2'] = `每日摄入总能量建议：` + json['energy'] + `(kcal/日);每日应摄入的食物交换份总数：` + json['changeNum'] +
							`（份/日）`;

						let eatData = [];
						let e1 = {
							name: '早餐',
							time: json['monTime'],
							energy: json['monEat'],
							remark: json['monRemark']
						};
						let e2 = {
							name: '早加',
							time: json['monAddTime'],
							energy: json['monAdd'],
							remark: json['monAddRemark']
						};
						let e3 = {
							name: '午餐',
							time: json['noonTime'],
							energy: json['noonEat'],
							remark: json['noonRemark']
						};
						let e4 = {
							name: '午加',
							time: json['noonAddTime'],
							energy: json['noonAdd'],
							remark: json['noonAddRemark']
						};
						let e5 = {
							name: '晚餐',
							time: json['nighTime'],
							energy: json['nighEat'],
							remark: json['nighRemark']
						};
						let e6 = {
							name: '晚加',
							time: json['nighAddTime'],
							energy: json['nighAdd'],
							remark: json['nighAddRemark']
						};
						eatData.push(e1, e2, e3, e4, e5, e6);

						data['3'] = eatData;

						// data['温馨提示'] = json['tips'];

						data['要求减重期间不吃的食物'] = json['request'];
					}
				}
				return data;
			},
			sportGuideData() {
				let data = {};
				if (this.inteData && this.inteData.forms && this.inteData.forms.length > 0) {
					let forms = this.inteData.forms;
					let aimForm = forms.find(item => item.formKey === 'sport_guide');
					if (aimForm) {
						let json = JSON.parse(aimForm.formValue);
						data['运动'] = `每天根据自身情况，安排` + json['sportLong'] + `分钟` + json['sportText'];
						data['推荐'] = `每日热量消耗建议` + json['energyLost'] + `（kcal/日），` + json['energyText'];
						data['建议有氧运动'] = `每周运动 ` + json['yyWeekTime'] + ` 次，每次 ` + json['yyTime'] + `分钟 ，心率达到 ` + json[
							'yyRate'] + ` 次/分钟 。建议运动项目：` + json['yySports'];
						data['建议抗阻运动'] = `每周运动 ` + json['kzWeekTime'] + ` 次，每次 ` + json['kzEveryTime'] + `分钟 ，一次 ` + json[
							'kzStrength'] + `组，每次` + json['groupNum'] + `组。建议运动项目：` + json['kzSports'];
						data['运动前后注意事项'] = json['notice'];
					}
				}
				return data;
			},
			interPlanData() {
				let data = {};
				if (this.inteData && this.inteData.forms && this.inteData.forms.length > 0) {
					let forms = this.inteData.forms;
					let aimForm = forms.find(item => item.formKey === 'inter_plan');
					if (aimForm) {
						let json = JSON.parse(aimForm.formValue);
						console.log("interPlanData", json);
						data['定期评估'] = "每隔" + json['evaCycle'] + "进行一次减重评估，视情况动态调整干预指标。";
						data['定期评估计划'] = json['evaContent'];
						data['跟踪随访'] = json['followContent'];
					}
				}
				return data;
			},
			lifeGuideData() {
				let data = [];
				if (this.inteData && this.inteData.forms && this.inteData.forms.length > 0) {
					let forms = this.inteData.forms;
					let aimForm = forms.find(item => item.formKey === 'life_style');
					if (aimForm) {
						let json = JSON.parse(aimForm.formValue);
						let key = json['eduText'];
						let eduContent = json['eduContent'];
						let key2 = json['styleText'];
						let styleContent = json['styleContent'];
						data.push({
							key: '健康宣教',
							value: eduContent
						});
						data.push({
							key: '生活方式',
							value: styleContent
						});
					}
				}
				return data;
			},
			aimData() {
				let data = [];
				if (this.inteData && this.inteData.forms && this.inteData.forms.length > 0) {
					let forms = this.inteData.forms;
					let aimForm = forms.find(item => item.formKey === 'wl_aim');
					if (aimForm) {
						let json = JSON.parse(aimForm.formValue);

						let d = [{
								name: '体重kg',
								key: 'Weight'
							},
							{
								name: '腰围(cm)',
								key: 'Waist'
							},
							{
								name: '体脂率(%)',
								key: 'Scr'
							},
							{
								name: '内脏脂肪面积(cm²)',
								key: 'Bfp'
							},
							{
								name: '高密度脂蛋白(HDL-C)',
								key: 'Hdlc'
							},
							{
								name: '尿酸(umol/L)',
								key: 'Ua'
							},
							{
								name: '肌酐(umol/L)',
								key: 'Scr'
							},
							{
								name: '体质指数(kg/m2)',
								key: 'Bmi'
							},
							{
								name: '血糖(mmol/L)',
								key: 'Sugar'
							},
							{
								name: '水分含量【占体重】(%)',
								key: 'Water'
							},
							{
								name: 'ALT(U/L)',
								key: 'Alt'
							},
							{
								name: 'AST(U/L)',
								key: 'Ast'
							},
							{
								name: '甘油三脂(TG)',
								key: 'Tg'
							},
							{
								name: '低密度脂蛋白(LDL-C)',
								key: 'Ldlc'
							}
						];

						data = d.map(item => {
							return {
								name: item.name,
								original: json['start' + item.key],
								aim: json['aim' + item.key + '1'] + '~' + json['aim' + item.key + '2']
							};
						});
					}
				}
				return data;
			}
		},
		methods: {
			// 获取饮酒状态文本
			getDrinkStatusText(status) {
				switch(status) {
					// case 1: return '是';
					// case 2: return '否';
					case 3: return '从不饮酒';
					case 4: return '偶尔饮酒';
					case 5: return '酗酒';
					default: return '未知';
				}
			},
			// 处理展开折叠的方法
			toggleExpand(index) {
				console.log('展开');
				// 使用 this.$set 确保 Vue 能够检测到数组的变化
				this.$set(this.expanded, index, !this.expanded[index]);
			},
			selectTag(e) {
				console.log(e);
				this.actionsName = e;
			},
			getPatientInfo() {
				UserApi.getPatientInfo().then(res => {
					if (res.code == 200) {
						this.age = res.data.age;
						console.log('进入2');
					}
				});
			},
			eatscrolltolower() {
				if (this.eatTotal <= this.eatList.length) {
					uni.$u.toast("没有更多数据了");
				} else {
					this.eatQueryParams.pageNum++;
					this.getUserEatList();
				}
			},
			foodImg(imgUrl) {
				console.log("imgUrl", imgUrl);
				if (imgUrl && !imgUrl.startsWith("http")) {
					return appConfig.getPicUrl() + imgUrl;
				} else if (imgUrl && imgUrl.startsWith("http")) {
					return imgUrl;
				} else {
					return 'https://fat-**********.cos.ap-nanjing.myqcloud.com/weixin/defaultFood.jpg';
				}
			},
			jiSuanEnergy(eat) {
				if (eat.fatEatItemList && eat.fatEatItemList.length > 0) {
					const total = eat.fatEatItemList.reduce((t, e) => t + e.energy, 0);
					return total + " kcal";
				} else {
					return "0 kcal";
				}
			},
			saveRemark() {
				let query = {
					idNo: this.patientInfo.idNo,
					remark: this.patientInfo.params.center.remark,
					pastHistory: this.patientInfo.params.center.pastHistory,
					drugHistory: this.patientInfo.params.center.drugHistory
				};
				PatientApi.updateCenter(query).then(res => {
					if (res.code == 200) {
						console.log('修改成功');
					}
				});
			},
			updateTag() {
				let query = {
					id: this.patientInfo.id,
					assignHos: this.$store.state.userInfo.caszUnit.id,
					tags: this.patientInfo.tags,
					tagIds: this.patientInfo.tagIds
				};
				PatientApi.updatePatient(query).then(res => {
					if (res.code == 200) {
						console.log('修改成功');
					}
				});
			},
			deleteTag(tag, index) {
				this.formattedTags.splice(index, 1);
				this.formattedTagIds.splice(index, 1);

				this.patientInfo.tags = this.formattedTags.join(',');
				this.patientInfo.tagIds = this.formattedTagIds.join(',');

				this.updateTag();
			},
			tagSelect(e) {
				this.formattedTags.push(e.name);
				this.formattedTagIds.push(e.value);
				this.patientInfo.tags = this.formattedTags.join(',');
				this.patientInfo.tagIds = this.formattedTagIds.join(',');
				this.showTag = false;
				this.updateTag();
			},
			getTagList() {
				let query = {
					unitId: this.$store.state.userInfo.caszUnit.id,
					pageSize: 20,
					pageNum: 1
				};
				TagApi.tagList(query).then(res => {
					if (res.code == 200) {
						this.tagList = [];
						res.rows.forEach(row => {
							this.tagList.push({
								name: row.tagName,
								value: row.id
							});
						});
					}
					console.log("taglist", this.tagList);
				});
			},
			otherDiease(dieases) {
				if (dieases) {
					return JSON.parse(dieases).join("、");
				} else {
					return '';
				}
			},
			changeTabs(tab) {
				this.tabsIndex = tab;
				console.log(tab);
				if (tab == 1) { //就诊报告
					this.getPatientVisitList();
				}
				if (tab == 2) { //减重报告
					this.getUserCurInte();
				}
				if (tab == 3) {
					this.getUserEvas();
				}
				if (tab == 4) {
					this.getEatStatics();
				}
				if (tab == 5) {
					this.getChartData('体重');
					this.getChartData('腰围');
					this.getChartData('运动');
				}
				if (tab == 6) {
					this.getPatientWarns();
				}
				if (tab == 7) {
					//查询用户的餐饮记录
					this.getUserEatList();
				}
			},
			getUserEatList() {
				this.eatQueryParams.idNo = this.patientInfo.idNo;
				EatApi.getUserPageEatList(this.eatQueryParams).then(res => {
					console.log("查询用户的餐饮记录", res);
					if (res.code == 200) {
						this.eatTotal = res.total;
						this.eatList = [...res.rows, ...this.eatList];
					}
				});
			},
			/**
			 * 编辑处理预警
			 * @param {Object} warn
			 */
			editWarnRecord(warn) {
				console.log("处理预警：", warn);
				this.warnEditShow = true;
				this.warnForm.id = warn.id;
				this.warnForm.name = this.patientInfo.name;
				this.warnForm.content = warn.content;
				this.warnForm.dealRemark = warn.dealRemark;
				this.warnForm.patientId = warn.patientId;
			},
			confirmEdit() {
				if (this.warnForm.dealRemark) {
					if (this.warnForm.oper == 'im') this.warnForm.imContent = this.warnForm.dealRemark;
					WarnApi.dealWarnRecord(this.warnForm).then(res => {
						uni.$u.toast("处理成功");
						this.warnEditShow = false;
						this.warnQueryParams.pageNum = 1;
						this.warnRecords = [];
						this.getPatientWarns();
					});
				} else {
					uni.$u.toast("请填写处理备注");
				}
			},
			/**
			 * 患者预警触底加载
			 */
			warnScrollToLower() {
				if (this.warnTotal > this.warnRecords.length) {
					this.warnQueryParams.pageNum++;
					this.getPatientWarns();
				} else {
					uni.$u.toast("没有更多数据了");
				}
			},
			/**
			 * 查询患者预警记录
			 */
			getPatientWarns() {
				this.warnQueryParams.patientId = this.patientId;
				WarnApi.patientWarnList(this.warnQueryParams).then(res => {
					if (res.code == 200) {
						this.warnRecords = [];
						this.warnTotal = res.total;
						this.warnRecords = [...this.warnRecords, ...res.rows];
					}
				});
			},
			/**
			 * 查询体征数据曲线
			 */
			getChartData(signType) {
				let query = {
					signTime: dayjs().format("YYYY-MM-DD"),
					signType: signType,
					during: this.during,
					patientId: this.patientId
				};
				SignApi.getSignChartData(query).then(res => {
					console.log("近7天" + signType, res);
					if (res.code == 200) {
						if (res.data) {
							let data = res.data;
							let months;
							let values;
							if (this.during == 'day') {
								if (signType == '体重') {
									// months = data.map(item => item.signTime.substring(5, 10))
									// values = data.map(item => item.weight)
									months = data.months;
									values = data.values;
								}
								if (signType == '腰围') {
									// months = data.map(item => item.signTime.substring(5, 10))
									// values = data.map(item => item.waist)
									months = data.months;
									values = data.values;
								}
								if (signType == '运动') {
									months = data.months;
									values = data.values;
								}
							}
							console.log(months, values);
							if (signType == '体重' && data.months.length > 0) {
								this.weightChartData = {
									"categories": months,
									"series": [{
										"name": "日期",
										"data": values,
										"color": "#4f41ac"
									}]
								};
							}
							if (signType == '腰围' && data.months.length > 0) {
								this.waistChartData = {
									"categories": months,
									"series": [{
										"name": "日期",
										"data": values,
										"color": "#4f41ac"
									}]
								};
							}
							if (signType == '运动' && data.months.length > 0) {
								this.sportChartData = {
									"categories": months,
									"series": [{
										"name": "日期",
										"data": values,
										"color": "#4f41ac"
									}]
								};
							}
							console.log('腰围', this.waistChartData);
							console.log('运动', this.sportChartData);
						}
					}
				});
			},
			/**
			 * 查询饮食分析
			 */
			getEatStatics() {
				this.mainActive = Object.assign({}, this.mainNutrition[0]);
				this.otherActive = Object.assign({}, this.otherNutrition[0]);
				this.getNutrChartData(this.mainActive.type, this.mainDuring, 'main');
				this.getNutrChartData(this.otherActive.type, this.otherDuring, 'other');
			},
			tabClickMain(item) {
				this.mainActive = item;
				this.getNutrChartData(this.mainActive.type, this.mainDuring, 'main');
			},
			tabClickOther(item) {
				this.otherActive = item;
				this.getNutrChartData(this.otherActive.type, this.otherDuring, 'other');
			},
			changeMainDuring(during) {
				this.mainDuring = during;
				this.getNutrChartData(this.mainActive.type, this.mainDuring, 'main');
			},
			changeOtherDuring(during) {
				this.otherDuring = during;
				this.getNutrChartData(this.otherActive.type, this.otherDuring, 'other');
			},
			getNutrChartData(nutr, during, type) {
				let params = {
					signTime: dayjs().format("YYYY-MM-DD"),
					nutrType: nutr,
					during: during,
					patientId: this.patientId
				};
				EatApi.getEatChartData(params).then(res => {
					console.log("getEatChartData", res);
					let chatRes = {
						categories: res.data.months,
						series: [{
								name: "推荐摄入量",
								lineType: 'dash',
								data: res.data.suggest,
								textColor: "#8b5fdc",
								color: "#8b5fdc",
								textSize: 12
							},
							{
								name: "实际摄入量",
								data: res.data.values,
								textColor: "#4f41ac",
								color: "#4f41ac",
								textSize: 12
							}
						]
					};
					if (type == 'main') {
						this.mainChartData = JSON.parse(JSON.stringify(chatRes));
					} else if (type == 'other') {
						this.otherChartData = JSON.parse(JSON.stringify(chatRes));
					}
				});
			},
			formatAdvice(advice) {
				// 对 advice 进行转义处理
				const escapedAdvice = advice.replace(/[\u0000-\u001F]/g, '');
				try {
					const adviceObj = JSON.parse(escapedAdvice);
					const formatted = [];
					for (let section in adviceObj) {
						const points = [];
						for (let point in adviceObj[section]) {
							points.push({
								title: point,
								content: adviceObj[section][point]
							});
						}
						formatted.push({
							title: section,
							points: points
						});
					}
					return formatted;
				} catch (error) {
					console.error('解析 advice 时出错:', error);
					return [];
				}
			},
			/**
			 * 查询就诊报告
			 */
			getPatientVisitList() {
				let query = {
					pageSize: 1000,
					pageNum: 1,
					patientId: this.patientId
				};
				HealthApi.patientVisitList(query).then(res => {
					if (res.code == 200) {
						this.visits = res.rows;
						// 初始化展开状态
						this.expanded = new Array(this.visits.length).fill(false);
						console.log("就诊评价", this.expanded, this.visits.length);
						// 处理每个对象的 advice
						this.formattedAdvice = this.visits.map(item => {
							return item.advice ? this.formatAdvice(item.advice) : [];
						});
					}
				});
			},
			/**
			 * 查询阶段评估
			 */
			getUserEvas() {
				HealthApi.patientEvaList({
					pageNum: 1,
					pageSize: 10000,
					patientId: this.patientId
				}).then(res => {
					if (res.code == 200) {
						this.evas = res.rows;
					}
				});
			},
			/**
			 * 查询当前干预计划
			 */
			getUserCurInte() {
				EatApi.getUserCurIntervene({
					patientId: this.patientId
				}).then(res => {
					console.log("健康干预表单", res)
					if (res.code == 200) {
						let forms = res.data.forms
						if (forms && forms.length > 0) {

							this.inteData = res.data
							let mnForms = forms.filter(item => item.formKey == 'medical_nutrition')
							if (mnForms.length > 0) {
								let formValue = mnForms[0].formValue
								if (formValue) {
									this.medNutri = JSON.parse(formValue)
									console.log(this.medNutri)
								}
							}

							let fv = forms[0].formValue

							let fvjson = JSON.parse(fv)
							console.log("转化成json：",fvjson)
							this.sections[0].content = fvjson.LifestyleIntervention;
							this.sections[1].content = fvjson.DietPlan;
							this.sections[2].content = fvjson.ScientificExercisePlan;
							this.sections[3].content = fvjson.BehaviorManagement;
							this.sections[4].content = fvjson.OtherPrecautions;

							console.log('sections', fvjson)
							this.inteReport = fvjson

						}
					}
				})
			},
		}
	}
</script>

<style lang="scss" >
	.index-tabs {
		overflow: scroll;
		position: sticky;
		// padding-top: 24rpx;
		padding-left: 30rpx;
		background: #FFFFFF;
		z-index: 11;

		&-one {
			display: inline-block;
			border-radius: 24rpx;
			background: #f1f1f1;
			color: #999;
			font-size: 28rpx;
			margin-right: 20rpx;
			padding: 10rpx 20rpx;
		}

		&-active {
			background: #0090d0;
			color: #FFFFFF;
		}
	}

	.ganyucontain {
		background-color: #f9f9f9;
		min-height: 100vh;

	}

	.contain {
		background-color: #f9f9f9;
		min-height: 100vh;
		padding: 30rpx;

		&-base {
			background: #FFFFFF;
			height: 120rpx;
			display: flex;
			align-items: center;
			padding: 0 24rpx;
			color: #696969;

			&-msg {
				font-size: 32rpx;
				flex: 1;
				margin-left: 30rpx;
			}
		}

		&-weight {
			padding: 30rpx;
			background: #FFFFFF;
			height: 550rpx;
			margin-top: 30rpx;
			position: relative;
		}

		&-title {
			position: relative;
			z-index: 1;
			font-size: 36rpx;
			font-weight: bold;
			margin-bottom: 20rpx;

			&::after {

				position: absolute;
				bottom: -10rpx;
				left: 0rpx;
				content: "";
				z-index: -1;
				width: 160rpx;
				height: 20rpx;
				background: linear-gradient(to right, #0090d0, transparent);
			}
		}

		&-unit {
			position: absolute;
			right: 30rpx;
			top: 30rpx;
			font-size: 32rpx;
			color: #999;
		}

		&-tabs {
			display: flex;
			padding: 30rpx 0;

			&-one {
				flex: 1;
				font-size: 32rpx;
				font-weight: bold;
				color: #AFAFAF;
				text-align: center;
				position: relative;
			}

			&-active {
				color: #0090d0;

				&::after {
					content: "";
					position: absolute;
					left: 50%;
					transform: translateX(-50%);
					bottom: -30rpx;
					width: 50rpx;
					height: 10rpx;
					border-radius: 50rpx;
					background-color: #0090d0;
				}
			}
		}

		&-assess {
			padding: 30rpx;
			background: #FFFFFF;

			&-head {
				display: flex;
				align-items: center;
				justify-content: space-between;

				&-date {
					font-size: 28rpx;
					margin-right: 10rpx;
				}
			}

			&-content {
				background: #F8F9FA;
				font-size: 30rpx;
				line-height: 1.5;
				padding: 30rpx;
				color: #6D6D6D;
				margin-top: 40rpx;
			}
		}

		&-option {
			height: 500rpx;
			background: #FFFFFF;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}



	.index-block {
		padding: 40rpx;
		background-color: #fff;
	}

	.index-block-title {
		font-size: 40rpx;
		font-weight: bold;
		padding: 0 0 40rpx 0;
	}

	.item-tj {
		width: 100%;
		height: 160rpx;
		border-radius: 16rpx;

		&-frist {
			background-color: rgba($color: #2979ff, $alpha: 0.8);
			// background-image: url('/static/img/bg/qb.png');
		}

		&-second {
			background-color: rgba($color: #303133, $alpha: 0.8);
			// background-image: url('/static/img/bg/qb.png');
		}

		&-thrid {
			background-color: rgba($color: #19be6b, $alpha: 0.8);
			// background-image: url('/static/img/bg/qb.png');
		}
	}

	.detail_list {
		height: 700rpx;
		overflow: auto;
		color: #9E9E9E;

		.detail_item {
			display: flex;
			margin: 20rpx 0;
			align-items: center;

			.icon {
				width: 30%;
				text-align: center;

				li {
					font-size: 80rpx;
				}
			}

			.right_content {
				width: 50%;
				text-align: center;
			}

			.icon-income {
				color: #4AABF9;
			}

			.icon-expend {
				color: #E45521;
			}

			.money {
				color: #000;
			}
		}
	}


	/* 请根据实际需求修改父元素尺寸，组件自动识别宽高 */
	.charts-box {
		width: 100%;
		height: 300px;
		margin-top: 25px;
	}

	.uni-card-title {
		flex: 1;
		font-weight: bold;
		font-size: 18px;
	}

	.flex {
		display: flex;
	}

	.font--grey {
		color: #919191
	}

	.font12 {
		font-size: 12px;
	}

	.indent5 {
		text-indent: 5px;
		display: inline-block;
	}

	.data-range {
		width: 20%;
		height: 30px;
		line-height: 30px;
		background: #F8F9FA;
		border: none;
		border-radius: 20px;
		font-size: 16px;
		text-align: center;
	}

	.data-range--active {
		background-color: #4f41ac;
		color: #fff;
	}

	.word-weight {

		padding: 30rpx;
		background: #FFFFFF;
		height: 640rpx;
		margin: 30rpx 0;
		position: relative;

		&-title {
			position: relative;
			z-index: 1;
			font-size: 36rpx;
			font-weight: bold;
			margin-bottom: 20rpx;

			&::after {

				position: absolute;
				bottom: -10rpx;
				left: 0rpx;
				content: "";
				z-index: -1;
				width: 160rpx;
				height: 20rpx;
				background: linear-gradient(to right, #4f41ac, transparent);
			}
		}

		&-during {
			display: flex;
			justify-content: space-around;

			&-one {
				background: #f9f9f9;
				color: #000;
				width: 120rpx;
				height: 60rpx;
				text-align: center;
				line-height: 60rpx;
				border-radius: 30rpx;
				font-size: 28rpx;
			}

			&-active {
				background: #0090d0;
				color: #FFF;
			}
		}

		&-unit {
			position: absolute;
			right: 30rpx;
			top: 30rpx;
			font-size: 28rpx;
			color: #999;
		}

		&-button {
			display: flex;
			justify-content: center;
			// margin-top: 80rpx;

			view {
				border: 1px solid #4f41ac;
				color: #4f41ac;
				border-radius: 50rpx;
				width: 340rpx;
				height: 60rpx;
				text-align: center;
				line-height: 60rpx;
			}

		}
	}

	.album-margin-top {
		margin-top: 20px;
		/* 或您想要的其他值 */
		display: flex;
		justify-content: flex-end;
	}

	.tag-container {
		display: flex;
		flex-wrap: wrap;
	}

	.tag-wrapper {
		display: flex;
		align-items: center;
		margin-right: 10px;
		/* 可选，用于标签之间的间距 */
		background-color: #4f41ac;
		margin-bottom: 10px;
		/* 可选，用于标签之间的垂直间距 */
	}

	.tag-text {
		padding: 5px 10px;
		background-color: #4f41ac;
		color: white;
		/* 背景颜色 */
		border-radius: 3px;
		/* 圆角 */
		cursor: default;
		/* 鼠标悬停时的样式 */
		/* 可添加其他样式，如字体、颜色等 */
	}

	.tag-wrapper u-icon {
		margin-left: 5px;
		/* 图标与标签文本之间的间距 */

	}

	.action-container {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		/* 均匀分布子元素 */
		/* 如果你想设置最大宽度，可以添加以下属性 */
		/* max-width: calc(100% - 40px); （考虑到左右各20px的padding） */
		padding: 0 20px;
		/* 给容器左右添加padding，而不是给子元素 */
	}

	.action-item {
		display: flex;
		align-items: center;
		/* 使用margin代替gap，为了兼容性 */
		color: #656D8E;

		box-sizing: border-box;
		/* 确保padding和border不会影响元素宽度计算 */
	}


	.fenge {
		margin-top: 10px;
	}

	.uni-card {
		margin-bottom: 20px;
		/* 卡片之间的间距 */
	}


.uni-card__title {
  font-weight: bold;
  font-size: 17px;
}
	// 新增样式，为 <pre> 标签设置样式
	.content {
		// white-space: pre-wrap;
		word-wrap: break-word;
	}
.ganyutitle {
	margin: 7px;
    font-size: 17px; // 可根据需要调整字体大小
    font-weight: bold; // 加粗字体
}
</style>
