<template>
	<view style="margin: 20px;">
		<Navbar :hideBtn="false" title="问卷详情" :placeholder="false" bgColor="#b7bbfa" :h5Show="false" :fixed="true" :titleStyle="{color:'#FFFFFF', fontSize:'16px', fontWeight:'500'}"></Navbar>
		<dynamic-form v-if="show1" :showSubmittBtn="true" @formSubmit="formSubmit" :screen-form="screenForm"
			:value="formData"></dynamic-form>
	</view>
</template>

<script>
	import DynamicForm from "@/pageWork/components/uni-active-form/dynamic-form";
	import * as Dynamic<PERSON><PERSON> from "@/api/dynamicForm.js"
	import * as Screening<PERSON>pi from "@/api/screening.js"
	import * as Question<PERSON><PERSON> from "@/api/questions.js"
	import Navbar from '@/components/navbar/Navbar'
	export default {
		components: {
			DynamicForm,
			Navbar
		},
		
		data() {
			return {
				screenId: null,
				patientId:null,
				show1: false,
				formData: {},
				screenForm: {
					'formText': "{\n  \"list\": [  ]\n}"
				},
				commitForm: null,
				patient:null,
				unitId:'',
			}
		},
		onLoad(option) {
				console.log("option",option)
				
			if(option.examId){//修改
				this.commitForm = uni.getStorageSync("editExamRecord")
				this.screenId = this.commitForm.formId;
				this.formData = JSON.parse(this.commitForm.formValue)
				uni.removeStorageSync("editExamRecord")
			}else{//新增
				this.screenId = option.formId;
				this.patientId = option.patientId;
				this.patient = uni.getStorageSync("selectedPatient")
				console.log("patient",this.patient)
				uni.removeStorageSync("selectedPatient")
				if(this.patient){
					//this.formData = this.patient
					// Object.assign(this.formData,this.patient)
					let {idNo,bornDate,gender,selfMobile,educationLevel,name,job} = this.patient;
					this.formData.idNo = idNo
					this.formData.bornDate = bornDate
					this.formData.sex = gender
					this.formData.phone = selfMobile
					this.formData.edu = educationLevel
					this.formData.name = name
					this.formData.job = job
				}
			}
			if (this.$hasAnyRoles("doctor,hos")) {
				console.log(this.$store.state)
				this.unitId = this.$store.state.userInfo.caszUnit.id;
			} else if (this.$hasAnyRoles("patient")) {
				this.unitId = this.$store.state.unit.id;
				this.patientId = (this.patientId==undefined ||this.patientId=='undefined' || this.patientId==null || this.patientId=='')? this.$store.state.unit.unionId:this.patientId;
			}
			console.log("this.screenId",this.screenId)
			this.getDynamicForm()
			
		},
		methods: {
			getDynamicForm() {
				DynamicApi.getById(this.screenId).then(res => {
					if (res.code == 200) {
						this.screenForm = res.data
						console.log("----------",this.screenId)
						this.screenForm.formText = res.data.formDesignerText
						this.show1 = true
					}
				})
			},
			formSubmit(data) {
				let id = null;
				if (this.commitForm) {
					id = this.commitForm.id
				}
				QuestionApi.addOrUpdateExamResult({
					id: id,
					examType: this.screenForm.formType,
					formId: this.screenForm.id,
					formKey: this.screenForm.formKey,
					formText: this.screenForm.formText,
					formValue: JSON.stringify(data),
					formName: this.screenForm.formName,
					actionType: this.screenForm.actionType,
					formType: this.screenForm.formType,
					patientId:this.patientId,
					unitId:this.unitId
				}).then(res => {
					if (res.code == 200) {
						uni.showToast({
							title: '提交成功',
							icon: 'none'
						})
						uni.navigateBack()
					}
				})
			},
		}
	}
</script>

<style>

</style>