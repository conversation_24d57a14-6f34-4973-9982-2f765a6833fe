import request from '@/config/request.js';


//查询动态标签
export const tagList = (params)=>request.get("/sysSet/tag/list",params,{custom:{isBgApi:true}})

//根据标签查患者
export const patientList = (params)=>request.get("/sysSet/tag/PatientList",params,{custom:{isBgApi:true}})

//查询收藏文章
export const collectList = (params)=>request.get("/system/collect/list",params,{custom:{isBgApi:true}})

//删除收藏文章
export const deleteCollectList = (params)=>request.get("/system/collect/delete",params,{custom:{isBgApi:true}})

//添加收藏文章
export const addCollectList = (params)=>request.post("/system/collect",params,{custom:{isBgApi:true}})
