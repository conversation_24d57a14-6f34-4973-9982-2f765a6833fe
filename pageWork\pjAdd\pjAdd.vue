<template>
	<view class="pj">
		<view class="pj-current">
			<view class="pl">
				<u--form labelPosition="top" labelWidth="120" :model="visitRecord" :rules="rules" ref="uForm">
					<u-form-item label="身高cm" prop="visitRecord.height" borderBottom>
						<u--input v-model="visitRecord.height" border="none"></u--input>
					</u-form-item>
					<u-form-item label="体重kg" prop="visitRecord.weight" borderBottom>
						<u--input v-model="visitRecord.weight" border="none"></u--input>
					</u-form-item>
					<u-form-item label="就诊报告" prop="visitRecord.content" borderBottom>
						<u--input v-model="visitRecord.content" border="none"></u--input>
					</u-form-item>
					<u-form-item label="就诊时间" prop="visitRecord.jzTime" borderBottom>
				  <picker
				    mode="date"
				    v-model="visitRecord.jzTime"
				    :range="dateRange"
				    @change="handleDateChange"
				  >
				    <view class="picker-view">
				      {{ formatDate(visitRecord.jzTime) || '请选择日期' }}
				    </view>
				  </picker>
					</u-form-item>
					<u-form-item label="就诊原因" prop="visitRecord.jzReason" borderBottom>
						<u--input v-model="visitRecord.jzReason" border="none"></u--input>
					</u-form-item>
					<u-form-item label="就诊医生" prop="visitRecord.jzDoctor" borderBottom>
						<u--input v-model="visitRecord.jzDoctor" border="none"></u--input>
					</u-form-item>
					<u-form-item label="检查图片">
						<u-upload :fileList="fileList1" @afterRead="afterRead" @delete="deletePic" name="1"
							multiple accept="image"></u-upload>
					</u-form-item>
				</u--form>
			</view>
			<view class="footer">
				<view class="footer-submit">
					<view class="footer-submit-button" @click="submitData">确定</view>
				</view>
			</view>
		</view>
	</view>

</template>

<script>
	import appConfig from "@/common/config";
	import * as UserApi from '@/api/user'
	import * as SignApi from "@/api/signCommit.js"
	import * as PatientApi from "@/api/patient.js"
	import * as HealthApi from "@/api/work/health.js"
	import {
		getPurePath,
		uploadForm
	} from "@/utils/common";
	export default {
		props: {
			signTime: {
				type: String,
				default: ''
			}
		},
		data() {
			return {
				rules: [],
				dateRange: [],
				visitRecord: {
					params: {},
					jzTime:"",
				},
				fileList1: [],
				flag: 0,
			}
			
		},
  watch: {

    'visitRecord.jzTime'(newVal, oldVal) {

      this.visitRecord.jzTime = this.formatDate(newVal);
    }
  },

		onShow() {
			this.visitRecord.unitId = this.$store.state.userInfo.caszUnit.id;


			if (this.$hasAnyRoles("patient")) {
				UserApi.patientCenterInfo().then(res => {
					if (res.code == 200) {
						this.score = res.data.score
					}
				})
			}


		},

		onLoad(option) {
			if (option.visitId) { //修改
				this.visitRecord = uni.getStorageSync("editVisitRecord");
				// 确保params对象存在
				if (!this.visitRecord.params) {
					this.visitRecord.params = {};
				}
				// 将图片数据转换为u-upload组件需要的对象数组格式
				if (this.visitRecord.params.pics && Array.isArray(this.visitRecord.params.pics)) {
					this.fileList1 = this.visitRecord.params.pics.map((item, index) => {
						// 如果item是字符串（URL），直接使用
						if (typeof item === 'string') {
							return {
								url: item,
								status: 'success',
								message: '',
								name: `image_${index}.jpg`
							};
						}
						// 如果item是对象，提取url属性
						else if (item && item.url) {
							return {
								url: item.url,
								status: 'success',
								message: '',
								name: item.fileName || item.originalFileName || `image_${index}.jpg`
							};
						}
						// 兜底处理
						return {
							url: item,
							status: 'success',
							message: '',
							name: `image_${index}.jpg`
						};
					});
				}
				console.log("visitRecord", this.visitRecord)
				console.log("visitRecord.params.pics", this.visitRecord.params.pics)
				console.log("fileList1", this.fileList1)

				uni.removeStorage({
					key: 'editVisitRecord'
				})
			} else { //新增
				this.visitRecord.patientId = option.id;
				// 确保新增时params对象也存在
				if (!this.visitRecord.params) {
					this.visitRecord.params = {};
				}
			}
		},

		methods: {
			handleDateChange(event) {

				this.visitRecord.jzTime = event.detail.value;
				console.log("回显", this.visitRecord.jzTime)

			},
			formatDate(date) {
				console.log("回显222", date)
				if (!date) return '请选择日期';
				// 假设 date 是一个 Date 对象或者可解析的日期字符串
				const d = new Date(date);
				const year = d.getFullYear();
				const month = (d.getMonth() + 1).toString().padStart(2, '0');
				const day = d.getDate().toString().padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			//提交
			submitData() {
				// 确保params对象存在
				if (!this.visitRecord.params) {
					this.visitRecord.params = {};
				}

				if (this.fileList1.length > 0) {
					let pics = this.fileList1.map(item => item.url)
					this.visitRecord.params.pics = pics
					console.log("this.visitRecord", this.visitRecord)
				} else {
					// 如果没有图片，设置为空数组
					this.visitRecord.params.pics = [];
				}
				if (this.visitRecord.id) { //update
					HealthApi.updateVisitRecord(this.visitRecord).then(res => {
						uni.$u.toast("修改成功")
						uni.navigateBack()
					})
				} else { //add
					HealthApi.addVisitRecord(this.visitRecord).then(res => {
						if (res.code == 200) {
							console.log("this.visitRecord", this.visitRecord)
							uni.$u.toast("新增成功")
							uni.navigateBack()
						}
					})
				}
			},

			chooseAndUploadImage() {
				uni.chooseImage({
					sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
					sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						const uploadTask = uni.uploadFile({
							url: appConfig.getPicUrl() +
								"/common/cosUploadPicture", // 这里填写你的上传图片的API地址
							filePath: tempFilePaths[0],
							name: 'file', // 这里根据API的要求来，可能是file或其他
							header: {
								'Authorization': 'Bearer ' + storage.get(ACCESS_TOKEN)
							},
							success: (uploadFileRes) => {
								var json = JSON.parse(uploadFileRes.data)
								console.log('图片上传成功', json.url);
							},
							fail: (uploadFileErr) => {
								console.error('图片上传失败', uploadFileErr);
							}
						});
						uploadTask.onProgressUpdate((res) => {
							console.log('上传进度' + res.progress + '%');
						});
					},
					fail: (chooseImageErr) => {
						console.error('选择图片失败', chooseImageErr);
					}
				});
			},
			// 删除图片
			deletePic(event) {
				this[`fileList${event.name}`].splice(event.index, 1)
				if (this.fileList1.length == 0) {
					this.flag = 0
				}
			},
			// 新增图片
			async afterRead(event) {
				console.log("afterRead event:", event);
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				console.log("afterRead lists:", lists);
				let fileListLen = this[`fileList${event.name}`].length
				lists.map((item) => {
					console.log("afterRead item:", item);
					this[`fileList${event.name}`].push({
						url: item.url,
						name: item.name || `image_${Date.now()}.jpg`,
						size: item.size || 0,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const fileName = lists[i].name || `image_${Date.now()}.jpg`;
					const result = await this.uploadFilePromise(lists[i].url, fileName)
					console.log("afterRead result:", result);
					// 直接替换整个对象，避免属性混乱
					const newItem = {
						url: result,
						name: fileName,
						size: lists[i].size || 0,
						status: 'success',
						message: ''
					};
					console.log("new item:", newItem);
					this[`fileList${event.name}`].splice(fileListLen, 1, newItem)
					fileListLen++
				}
				console.log("final fileList1:", this.fileList1);
			},

			uploadFilePromise(url, fileName) {
				let token = uni.getStorageSync("AccessToken")
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: appConfig.getPicUrl() + '/common/cosUpload', // 仅为示例，非真实的接口地址
						filePath: url,
						name: 'file',
						formData: {
							fileName: fileName || `image_${Date.now()}.jpg`
						},
						header: {
							Authorization: "Bearer " + token
						},
						success: (res) => {
							console.log("uploadFilePromise", res)
							let upload = JSON.parse(res.data)
							setTimeout(() => {
								resolve(upload.url)
							}, 1000)
						},
						fail: (error) => {
							console.error("upload failed:", error)
							reject(error)
						}
					});
				})
			},

		}

	}
</script>

<style scoped lang="scss">
	.pj {
		padding: 30rpx;
	}

	.footer {
		height: 200rpx;
	}

	.footer-submit {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		border-top: 1px solid #ddd;
		border-bottom: 1px solid #ddd;
		height: 130rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: #FFFFFF;
		text-align: center;
		z-index: 10000000;

		&-button {
			height: 90rpx;
			background: #4f41ac;
			width: 90%;
			color: #FFFFFF;
			text-align: center;
			line-height: 90rpx;
			border-radius: 50rpx;
		}
	}
</style>